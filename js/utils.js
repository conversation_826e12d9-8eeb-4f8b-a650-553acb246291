/**
 * 工具函数库
 * 提供通用的工具方法和辅助函数
 */

const Utils = {
    /**
     * 格式化日期时间
     * @param {string|Date} dateString - 日期字符串或Date对象
     * @param {string} format - 格式类型 ('datetime', 'date', 'time')
     * @returns {string} 格式化后的日期字符串
     */
    formatDateTime(dateString, format = 'datetime') {
        if (!dateString) return '-';
        
        const date = new Date(dateString);
        if (isNaN(date.getTime())) return '-';
        
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');
        
        switch (format) {
            case 'date':
                return `${year}-${month}-${day}`;
            case 'time':
                return `${hours}:${minutes}:${seconds}`;
            case 'datetime':
            default:
                return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        }
    },

    /**
     * 格式化文件大小
     * @param {number} bytes - 字节数
     * @returns {string} 格式化后的大小字符串
     */
    formatFileSize(bytes) {
        if (!bytes || bytes === 0) return '0 B';
        
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    /**
     * 格式化数字（添加千分位分隔符）
     * @param {number} num - 数字
     * @returns {string} 格式化后的数字字符串
     */
    formatNumber(num) {
        if (num === null || num === undefined) return '0';
        return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    },

    /**
     * 截断文本
     * @param {string} text - 原始文本
     * @param {number} maxLength - 最大长度
     * @param {string} suffix - 后缀
     * @returns {string} 截断后的文本
     */
    truncateText(text, maxLength = 50, suffix = '...') {
        if (!text) return '';
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength) + suffix;
    },

    /**
     * 防抖函数
     * @param {Function} func - 要防抖的函数
     * @param {number} delay - 延迟时间（毫秒）
     * @returns {Function} 防抖后的函数
     */
    debounce(func, delay = 300) {
        let timeoutId;
        return function (...args) {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(() => func.apply(this, args), delay);
        };
    },

    /**
     * 节流函数
     * @param {Function} func - 要节流的函数
     * @param {number} delay - 延迟时间（毫秒）
     * @returns {Function} 节流后的函数
     */
    throttle(func, delay = 300) {
        let lastCall = 0;
        return function (...args) {
            const now = Date.now();
            if (now - lastCall >= delay) {
                lastCall = now;
                return func.apply(this, args);
            }
        };
    },

    /**
     * 深拷贝对象
     * @param {any} obj - 要拷贝的对象
     * @returns {any} 拷贝后的对象
     */
    deepClone(obj) {
        if (obj === null || typeof obj !== 'object') return obj;
        if (obj instanceof Date) return new Date(obj.getTime());
        if (obj instanceof Array) return obj.map(item => this.deepClone(item));
        if (typeof obj === 'object') {
            const clonedObj = {};
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    clonedObj[key] = this.deepClone(obj[key]);
                }
            }
            return clonedObj;
        }
    },

    /**
     * 生成随机ID
     * @param {number} length - ID长度
     * @returns {string} 随机ID
     */
    generateId(length = 8) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    },

    /**
     * 验证邮箱格式
     * @param {string} email - 邮箱地址
     * @returns {boolean} 是否有效
     */
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    },

    /**
     * 验证URL格式
     * @param {string} url - URL地址
     * @returns {boolean} 是否有效
     */
    isValidUrl(url) {
        try {
            new URL(url);
            return true;
        } catch {
            return false;
        }
    },

    /**
     * 获取状态标签HTML
     * @param {number|string} status - 状态值
     * @param {string} type - 状态类型 ('request', 'verification')
     * @returns {string} HTML字符串
     */
    getStatusBadge(status, type = 'request') {
        if (type === 'request') {
            return status == 1 
                ? '<span class="badge bg-success"><i class="bi bi-check-circle me-1"></i>成功</span>'
                : '<span class="badge bg-danger"><i class="bi bi-x-circle me-1"></i>失败</span>';
        } else if (type === 'verification') {
            return status == 1 
                ? '<span class="badge bg-success"><i class="bi bi-shield-check me-1"></i>已验证</span>'
                : '<span class="badge bg-warning"><i class="bi bi-shield-exclamation me-1"></i>未验证</span>';
        } else if (type === 'manual_check') {
            return status == 1 
                ? '<span class="badge bg-info"><i class="bi bi-person-check me-1"></i>已确认</span>'
                : '<span class="badge bg-secondary"><i class="bi bi-person-dash me-1"></i>未确认</span>';
        }
        return '<span class="badge bg-secondary">未知</span>';
    },

    /**
     * 获取题目类型图标
     * @param {string} questionType - 题目类型
     * @returns {string} 图标HTML
     */
    getQuestionTypeIcon(questionType) {
        const icons = {
            '单选题': '<i class="bi bi-record-circle text-primary"></i>',
            '多选题': '<i class="bi bi-check2-square text-success"></i>',
            '判断题': '<i class="bi bi-question-circle text-info"></i>'
        };
        return icons[questionType] || '<i class="bi bi-question-circle text-muted"></i>';
    },

    /**
     * 显示Toast通知
     * @param {string} message - 消息内容
     * @param {string} type - 类型 ('success', 'error', 'warning', 'info')
     * @param {number} duration - 显示时长（毫秒）
     */
    showToast(message, type = 'info', duration = 3000) {
        // 创建toast容器（如果不存在）
        let toastContainer = document.getElementById('toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.id = 'toast-container';
            toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
            toastContainer.style.zIndex = '9999';
            document.body.appendChild(toastContainer);
        }

        // 创建toast元素
        const toastId = 'toast-' + this.generateId();
        const iconMap = {
            success: 'bi-check-circle-fill text-success',
            error: 'bi-x-circle-fill text-danger',
            warning: 'bi-exclamation-triangle-fill text-warning',
            info: 'bi-info-circle-fill text-info'
        };

        const toastHtml = `
            <div id="${toastId}" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="toast-header">
                    <i class="bi ${iconMap[type]} me-2"></i>
                    <strong class="me-auto">系统通知</strong>
                    <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                </div>
                <div class="toast-body">
                    ${message}
                </div>
            </div>
        `;

        toastContainer.insertAdjacentHTML('beforeend', toastHtml);

        // 显示toast
        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement, { delay: duration });
        toast.show();

        // 自动移除
        toastElement.addEventListener('hidden.bs.toast', () => {
            toastElement.remove();
        });
    },

    /**
     * 显示确认对话框
     * @param {string} message - 确认消息
     * @param {string} title - 标题
     * @returns {Promise<boolean>} 用户选择结果
     */
    async showConfirm(message, title = '确认操作') {
        return new Promise((resolve) => {
            // 创建模态框
            const modalId = 'confirm-modal-' + this.generateId();
            const modalHtml = `
                <div class="modal fade" id="${modalId}" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">${title}</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <p>${message}</p>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                <button type="button" class="btn btn-primary confirm-btn">确认</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modalHtml);

            const modalElement = document.getElementById(modalId);
            const modal = new bootstrap.Modal(modalElement);

            // 绑定事件
            modalElement.querySelector('.confirm-btn').addEventListener('click', () => {
                modal.hide();
                resolve(true);
            });

            modalElement.addEventListener('hidden.bs.modal', () => {
                modalElement.remove();
                resolve(false);
            });

            modal.show();
        });
    },

    /**
     * 显示加载状态
     * @param {HTMLElement} element - 目标元素
     * @param {boolean} show - 是否显示加载状态
     * @param {string} text - 加载文本
     */
    showLoading(element, show = true, text = '加载中...') {
        if (show) {
            element.innerHTML = `
                <div class="text-center py-4">
                    <div class="loading-spinner me-2"></div>
                    <span>${text}</span>
                </div>
            `;
        }
    },

    /**
     * 导出数据为CSV
     * @param {Array} data - 数据数组
     * @param {string} filename - 文件名
     * @param {Array} headers - 表头数组
     */
    exportToCSV(data, filename = 'export.csv', headers = []) {
        if (!data || data.length === 0) {
            this.showToast('没有数据可导出', 'warning');
            return;
        }

        let csvContent = '';
        
        // 添加表头
        if (headers.length > 0) {
            csvContent += headers.join(',') + '\n';
        }

        // 添加数据行
        data.forEach(row => {
            const values = Object.values(row).map(value => {
                // 处理包含逗号或引号的值
                if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
                    return `"${value.replace(/"/g, '""')}"`;
                }
                return value;
            });
            csvContent += values.join(',') + '\n';
        });

        // 创建下载链接
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        this.showToast('导出成功', 'success');
    },

    /**
     * 复制文本到剪贴板
     * @param {string} text - 要复制的文本
     * @returns {Promise<boolean>} 是否成功
     */
    async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            this.showToast('已复制到剪贴板', 'success');
            return true;
        } catch (error) {
            console.error('复制失败:', error);
            this.showToast('复制失败', 'error');
            return false;
        }
    }
};

// 全局可用
window.Utils = Utils;
