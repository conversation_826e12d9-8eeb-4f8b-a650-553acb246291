/**
 * 题库管理页面逻辑
 */

class QuestionManager {
    constructor() {
        this.currentPage = 1;
        this.currentFilters = {};
        this.currentView = 'table'; // 'table' 或 'card'
        this.questions = [];
        this.pagination = {};
        this.selectedQuestions = new Set();
        this.isEditing = false;
        this.editingId = null;
        
        this.init();
    }

    /**
     * 初始化页面
     */
    init() {
        this.bindEvents();
        this.loadQuestions();
        this.updateStatistics();
        
        // 设置默认日期范围（最近30天）
        this.setDefaultDateRange();
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 筛选表单提交
        document.getElementById('filterForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleFilter();
        });

        // 搜索框防抖
        const keywordInput = document.querySelector('input[name="keyword"]');
        keywordInput.addEventListener('input', Utils.debounce(() => this.handleFilter(), 500));

        // 题目类型变化事件
        document.getElementById('questionType').addEventListener('change', this.handleQuestionTypeChange.bind(this));
    }

    /**
     * 设置默认日期范围
     */
    setDefaultDateRange() {
        const endDate = new Date();
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - 30);

        document.querySelector('input[name="endDate"]').value = Utils.formatDateTime(endDate, 'date');
        document.querySelector('input[name="startDate"]').value = Utils.formatDateTime(startDate, 'date');
    }

    /**
     * 获取筛选条件
     */
    getFilters() {
        const formData = new FormData(document.getElementById('filterForm'));
        const filters = {
            page: this.currentPage,
            pageSize: parseInt(formData.get('pageSize')) || 50
        };

        // 添加非空的筛选条件
        ['keyword', 'questionType', 'isVerified', 'startDate', 'endDate', 'sort', 'order'].forEach(key => {
            const value = formData.get(key);
            if (value && value.trim() !== '') {
                filters[key] = value.trim();
            }
        });

        return filters;
    }

    /**
     * 处理筛选
     */
    async handleFilter() {
        this.currentPage = 1;
        this.currentFilters = this.getFilters();
        await this.loadQuestions();
    }

    /**
     * 加载题目数据
     */
    async loadQuestions() {
        this.showLoading(true);
        
        try {
            const result = await API.getQuestions(this.currentFilters);
            this.questions = result.data || [];
            this.pagination = result.pagination || {};
            
            this.renderQuestions();
            this.renderPagination();
            this.updateRecordInfo();
            this.clearSelection();
            
        } catch (error) {
            console.error('加载题目数据失败:', error);
            Utils.showToast('加载数据失败: ' + error.message, 'error');

            // 为了测试，添加一些模拟数据
            this.questions = [
                {
                    id: 1,
                    question_type: '单选题',
                    question_text: '驾驶机动车在以下哪种情况下不得超车？',
                    options: {
                        A: '左侧A柱盲区内可能有行人将要通过',
                        B: '右侧有车辆正在超车',
                        C: '前方车辆正在左转',
                        D: '道路条件良好'
                    },
                    answer: {"A": "左侧A柱盲区内可能有行人将要通过"},
                    is_verified: 1,
                    user_image: 'https://via.placeholder.com/150',
                    created_at: '2025-01-12 10:00:00',
                    updated_at: '2025-01-12 10:00:00'
                },
                {
                    id: 2,
                    question_type: '多选题',
                    question_text: '以下哪些行为属于危险驾驶？',
                    options: {
                        A: '服用国家管制的精神药品后驾驶机动车',
                        B: '正常行驶',
                        C: '遵守交通规则',
                        D: '饮酒后立即驾驶机动车'
                    },
                    answer: {"A": "服用国家管制的精神药品后驾驶机动车", "D": "饮酒后立即驾驶机动车"},
                    is_verified: 0,
                    user_image: 'https://via.placeholder.com/150',
                    created_at: '2025-01-12 11:00:00',
                    updated_at: '2025-01-12 11:00:00'
                },
                {
                    id: 3,
                    question_type: '判断题',
                    question_text: '驾驶机动车时可以使用手机通话。',
                    options: {
                        Y: '正确',
                        N: '错误'
                    },
                    answer: {"N": "错误"},
                    is_verified: 1,
                    user_image: 'https://via.placeholder.com/150',
                    created_at: '2025-01-12 12:00:00',
                    updated_at: '2025-01-12 12:00:00'
                }
            ];

            this.pagination = {
                page: 1,
                total_pages: 1,
                total: 3,
                has_prev: false,
                has_next: false
            };

            this.renderQuestions();
            this.renderPagination();
            this.updateRecordInfo();
            this.clearSelection();
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * 更新统计数据
     */
    async updateStatistics() {
        try {
            // 获取总数
            const totalResult = await API.getQuestions({ page: 1, page_size: 1 });
            const total = totalResult.pagination?.total || 0;
            
            // 获取已验证数量
            const verifiedResult = await API.getQuestions({ page: 1, page_size: 1, isVerified: 1 });
            const verified = verifiedResult.pagination?.total || 0;
            
            // 获取今日新增
            const today = Utils.formatDateTime(new Date(), 'date');
            const todayResult = await API.getQuestions({ page: 1, page_size: 1, startDate: today, endDate: today });
            const todayCount = todayResult.pagination?.total || 0;
            
            // 更新显示
            document.getElementById('totalQuestions').textContent = Utils.formatNumber(total);
            document.getElementById('verifiedQuestions').textContent = Utils.formatNumber(verified);
            document.getElementById('unverifiedQuestions').textContent = Utils.formatNumber(total - verified);
            document.getElementById('todayQuestions').textContent = Utils.formatNumber(todayCount);
            
        } catch (error) {
            console.error('更新统计数据失败:', error);
        }
    }

    /**
     * 渲染题目列表
     */
    renderQuestions() {
        if (this.questions.length === 0) {
            this.showEmptyState();
            return;
        }

        this.hideEmptyState();
        
        if (this.currentView === 'table') {
            this.renderTableView();
        } else {
            this.renderCardView();
        }
    }

    /**
     * 渲染表格视图
     */
    renderTableView() {
        const tbody = document.getElementById('questionsTableBody');
        tbody.innerHTML = '';

        this.questions.forEach(question => {
            const row = document.createElement('tr');
            row.className = this.selectedQuestions.has(question.id) ? 'selected' : '';
            row.innerHTML = `
                <td>
                    <input type="checkbox" class="form-check-input"
                           ${this.selectedQuestions.has(question.id) ? 'checked' : ''}
                           onchange="questionManager.toggleSelection(${question.id})">
                </td>
                <td>
                    <span class="fw-bold text-primary">#${question.id}</span>
                </td>
                <td>
                    ${this.getQuestionTypeDisplay(question.question_type)}
                </td>
                <td>
                    <div class="question-content-preview" title="${question.question_text}">
                        ${question.question_text}
                    </div>
                </td>
                <td>
                    <div class="small">
                        ${this.renderOptionsPreview(question)}
                    </div>
                </td>
                <td>
                    <span class="badge bg-success answer-display ${this.getAnswerDisplay(question).length > 50 ? 'long-content' : ''}" title="${this.getAnswerDisplay(question).replace(/<br>/g, ' | ')}">${this.getAnswerDisplay(question)}</span>
                </td>
                <td>
                    ${question.image_url ?
                        `<img src="${question.image_url}" class="image-thumbnail" onclick="showImageModal('${question.image_url}')" alt="题干图片" title="题干图片">` :
                        '<span class="text-muted">无图片</span>'
                    }
                </td>
                <td>
                    ${question.user_image ?
                        `<img src="${question.user_image}" class="image-thumbnail" onclick="showImageModal('${question.user_image}')" alt="用户图片" title="用户图片">` :
                        '<span class="text-muted">无图片</span>'
                    }
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn action-btn btn-edit" onclick="editQuestion(${question.id})" title="编辑">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn action-btn ${question.is_verified == 1 ? 'btn-verified' : 'btn-unverified'}"
                                onclick="toggleVerification(${question.id}, ${question.is_verified == 1 ? 0 : 1})"
                                title="${question.is_verified == 1 ? '已验证，点击取消验证' : '未验证，点击验证'}">
                            <i class="bi ${question.is_verified == 1 ? 'bi-shield-check' : 'bi-shield-exclamation'}"></i>
                        </button>
                        <button class="btn action-btn btn-delete" onclick="deleteQuestion(${question.id})" title="删除">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    /**
     * 渲染卡片视图
     */
    renderCardView() {
        const container = document.getElementById('questionsCardContainer');
        container.innerHTML = '';

        this.questions.forEach(question => {
            const card = document.createElement('div');
            card.className = 'col-md-6 col-lg-4';
            card.innerHTML = `
                <div class="question-card ${this.selectedQuestions.has(question.id) ? 'selected' : ''}">
                    <div class="question-checkbox">
                        <input type="checkbox" class="form-check-input" 
                               ${this.selectedQuestions.has(question.id) ? 'checked' : ''}
                               onchange="questionManager.toggleSelection(${question.id})">
                    </div>
                    <div class="question-card-header">
                        <div class="question-card-id">#${question.id}</div>
                        <div class="question-card-type">
                            ${this.getQuestionTypeDisplay(question.question_type)}
                        </div>
                    </div>
                    <div class="question-card-body">
                        <div class="question-text-card">${question.question_text}</div>
                        <div class="question-options">
                            ${this.renderOptionsPreview(question, true)}
                        </div>
                        <div class="mb-2">
                            <strong>答案:</strong>
                            <span class="badge bg-success answer-display ${this.getAnswerDisplay(question).length > 50 ? 'long-content' : ''}">${this.getAnswerDisplay(question)}</span>
                        </div>
                        ${question.image_url ?
                            `<div class="mb-2">
                                <strong>题干图片:</strong><br>
                                <img src="${question.image_url}" class="image-thumbnail" onclick="showImageModal('${question.image_url}')" alt="题干图片">
                            </div>` : ''
                        }
                        ${question.user_image ?
                            `<div class="mb-2">
                                <strong>用户图片:</strong><br>
                                <img src="${question.user_image}" class="image-thumbnail" onclick="showImageModal('${question.user_image}')" alt="用户图片">
                            </div>` : ''
                        }
                    </div>
                    <div class="question-card-footer">
                        <div class="question-card-actions">
                            <button class="btn btn-sm btn-outline-primary" onclick="editQuestion(${question.id})" title="编辑">
                                <i class="bi bi-pencil me-1"></i>编辑
                            </button>
                            <button class="btn btn-sm ${question.is_verified == 1 ? 'btn-verified-card' : 'btn-unverified-card'}"
                                    onclick="toggleVerification(${question.id}, ${question.is_verified == 1 ? 0 : 1})"
                                    title="${question.is_verified == 1 ? '已验证，点击取消验证' : '未验证，点击验证'}">
                                <i class="bi ${question.is_verified == 1 ? 'bi-shield-check' : 'bi-shield-exclamation'} me-1"></i>
                                ${question.is_verified == 1 ? '已验证' : '未验证'}
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteQuestion(${question.id})" title="删除">
                                <i class="bi bi-trash me-1"></i>删除
                            </button>
                        </div>
                    </div>
                </div>
            `;
            container.appendChild(card);
        });
    }

    /**
     * 获取题目类型显示
     */
    getQuestionTypeDisplay(type) {
        const typeMap = {
            '单选题': '<span class="question-type-icon question-type-single"><i class="bi bi-record-circle me-1"></i>单选题</span>',
            '多选题': '<span class="question-type-icon question-type-multiple"><i class="bi bi-check2-square me-1"></i>多选题</span>',
            '判断题': '<span class="question-type-icon question-type-truefalse"><i class="bi bi-question-circle me-1"></i>判断题</span>'
        };
        return typeMap[type] || '<span class="question-type-icon"><i class="bi bi-question-circle me-1"></i>未知</span>';
    }

    /**
     * 渲染选项预览
     */
    renderOptionsPreview(question, isCard = false) {
        if (!question.options) return '<span class="text-muted">无选项</span>';

        const options = question.options;
        const answer = question.answer?.correct;
        let html = '';

        // 获取正确答案的选项键
        let correctKeys = [];
        // 尝试多种可能的答案数据位置
        let answerData = question.answer?.correct || question.answer || question.correct_answer;

        if (answerData) {
            if (typeof answerData === 'object' && !Array.isArray(answerData)) {
                // 答案是对象格式（如示例中的JSON格式）
                correctKeys = Object.keys(answerData);
            } else if (Array.isArray(answerData)) {
                // 答案是数组格式
                correctKeys = answerData;
            } else if (typeof answerData === 'string') {
                // 答案是字符串格式
                correctKeys = [answerData];
            }
        }

        Object.keys(options).forEach(key => {
            const isCorrect = correctKeys.includes(key);
            const optionClass = isCorrect ? 'option-item correct' : 'option-item';

            if (isCard) {
                html += `<div class="${optionClass}">${key}: ${Utils.truncateText(options[key], 30)}</div>`;
            } else {
                html += `<div class="${optionClass}">${key}: ${Utils.truncateText(options[key], 20)}</div>`;
            }
        });

        return html;
    }

    /**
     * 获取答案显示
     */
    getAnswerDisplay(question) {
        // 尝试多种可能的答案数据位置
        let answer = question.answer?.correct || question.answer || question.correct_answer;

        // 如果没有答案数据，返回默认值
        if (!answer) {
            return '-';
        }

        // 如果答案是对象格式（如示例中的JSON格式）
        if (typeof answer === 'object' && !Array.isArray(answer)) {
            const answerKeys = Object.keys(answer);
            if (answerKeys.length === 0) {
                return '-';
            }

            // 构建完整的答案显示（包括选项字母和内容）
            const answerTexts = answerKeys.map(key => {
                const content = answer[key];
                return `${key}: ${content}`;
            });

            // 判断是单选题还是多选题
            if (answerTexts.length === 1) {
                // 单选题：显示完整内容
                return answerTexts[0];
            } else {
                // 多选题：多个答案换行显示
                return answerTexts.join('<br>');
            }
        }

        // 如果答案是数组格式（需要从选项中查找对应内容）
        if (Array.isArray(answer)) {
            const answerTexts = answer.map(key => {
                const content = question.options?.[key];
                if (content) {
                    return `${key}: ${content}`;
                } else {
                    return key; // 如果找不到对应选项，只显示字母
                }
            });

            if (answerTexts.length === 1) {
                // 单选题
                return answerTexts[0];
            } else {
                // 多选题：换行显示
                return answerTexts.join('<br>');
            }
        }

        // 如果答案是字符串格式（判断题等）
        if (typeof answer === 'string') {
            // 尝试从选项中查找对应内容
            const content = question.options?.[answer];
            if (content) {
                return `${answer}: ${content}`;
            } else {
                return answer; // 如果找不到对应选项，只显示原始值
            }
        }

        return '-';
    }



    /**
     * 渲染分页
     */
    renderPagination() {
        const pagination = document.getElementById('pagination');
        pagination.innerHTML = '';

        if (!this.pagination.total_pages || this.pagination.total_pages <= 1) {
            return;
        }

        const currentPage = this.pagination.page || 1;
        const totalPages = this.pagination.total_pages;

        // 上一页
        const prevLi = document.createElement('li');
        prevLi.className = `page-item ${!this.pagination.has_prev ? 'disabled' : ''}`;
        prevLi.innerHTML = `
            <a class="page-link" href="#" onclick="changePage(${currentPage - 1})">
                <i class="bi bi-chevron-left"></i>
            </a>
        `;
        pagination.appendChild(prevLi);

        // 页码
        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, currentPage + 2);

        if (startPage > 1) {
            const firstLi = document.createElement('li');
            firstLi.className = 'page-item';
            firstLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(1)">1</a>`;
            pagination.appendChild(firstLi);

            if (startPage > 2) {
                const ellipsisLi = document.createElement('li');
                ellipsisLi.className = 'page-item disabled';
                ellipsisLi.innerHTML = `<span class="page-link">...</span>`;
                pagination.appendChild(ellipsisLi);
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            const li = document.createElement('li');
            li.className = `page-item ${i === currentPage ? 'active' : ''}`;
            li.innerHTML = `<a class="page-link" href="#" onclick="changePage(${i})">${i}</a>`;
            pagination.appendChild(li);
        }

        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                const ellipsisLi = document.createElement('li');
                ellipsisLi.className = 'page-item disabled';
                ellipsisLi.innerHTML = `<span class="page-link">...</span>`;
                pagination.appendChild(ellipsisLi);
            }

            const lastLi = document.createElement('li');
            lastLi.className = 'page-item';
            lastLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${totalPages})">${totalPages}</a>`;
            pagination.appendChild(lastLi);
        }

        // 下一页
        const nextLi = document.createElement('li');
        nextLi.className = `page-item ${!this.pagination.has_next ? 'disabled' : ''}`;
        nextLi.innerHTML = `
            <a class="page-link" href="#" onclick="changePage(${currentPage + 1})">
                <i class="bi bi-chevron-right"></i>
            </a>
        `;
        pagination.appendChild(nextLi);
    }

    /**
     * 更新记录信息
     */
    updateRecordInfo() {
        const info = document.getElementById('recordInfo');
        if (this.pagination.total) {
            info.textContent = `共 ${Utils.formatNumber(this.pagination.total)} 条记录`;
        } else {
            info.textContent = '共 0 条记录';
        }
    }

    /**
     * 显示/隐藏加载状态
     */
    showLoading(show) {
        const loadingState = document.getElementById('loadingState');
        const tableView = document.getElementById('tableView');
        const cardView = document.getElementById('cardView');
        
        if (show) {
            loadingState.style.display = 'block';
            tableView.style.display = 'none';
            cardView.style.display = 'none';
            this.hideEmptyState();
        } else {
            loadingState.style.display = 'none';
            if (this.currentView === 'table') {
                tableView.style.display = 'block';
                cardView.style.display = 'none';
            } else {
                tableView.style.display = 'none';
                cardView.style.display = 'block';
            }
        }
    }

    /**
     * 显示空状态
     */
    showEmptyState() {
        document.getElementById('emptyState').style.display = 'block';
        document.getElementById('tableView').style.display = 'none';
        document.getElementById('cardView').style.display = 'none';
    }

    /**
     * 隐藏空状态
     */
    hideEmptyState() {
        document.getElementById('emptyState').style.display = 'none';
    }

    /**
     * 切换视图
     */
    toggleView(view) {
        this.currentView = view;
        
        // 更新按钮状态
        document.getElementById('tableViewBtn').classList.toggle('active', view === 'table');
        document.getElementById('cardViewBtn').classList.toggle('active', view === 'card');
        
        // 重新渲染
        this.renderQuestions();
    }

    /**
     * 切换页码
     */
    async changePage(page) {
        if (page < 1 || page > this.pagination.total_pages) return;

        this.currentPage = page;
        this.currentFilters.page = page;
        await this.loadQuestions();
    }

    /**
     * 切换选择状态
     */
    toggleSelection(questionId) {
        if (this.selectedQuestions.has(questionId)) {
            this.selectedQuestions.delete(questionId);
        } else {
            this.selectedQuestions.add(questionId);
        }

        this.updateSelectionUI();
        this.updateBatchActions();
    }

    /**
     * 全选/取消全选
     */
    toggleSelectAll() {
        const selectAllCheckbox = document.getElementById('selectAll') || document.getElementById('headerCheckbox');
        const isChecked = selectAllCheckbox.checked;

        if (isChecked) {
            // 全选当前页面的题目
            this.questions.forEach(question => {
                this.selectedQuestions.add(question.id);
            });
        } else {
            // 取消全选
            this.selectedQuestions.clear();
        }

        this.updateSelectionUI();
        this.updateBatchActions();
    }

    /**
     * 清空选择
     */
    clearSelection() {
        this.selectedQuestions.clear();
        this.updateSelectionUI();
        this.updateBatchActions();
    }

    /**
     * 更新选择UI
     */
    updateSelectionUI() {
        // 更新表格中的复选框
        const checkboxes = document.querySelectorAll('input[type="checkbox"][onchange*="toggleSelection"]');
        checkboxes.forEach(checkbox => {
            const onchangeAttr = checkbox.getAttribute('onchange');
            if (onchangeAttr) {
                const match = onchangeAttr.match(/\d+/);
                if (match && match[0]) {
                    const questionId = parseInt(match[0]);
                    checkbox.checked = this.selectedQuestions.has(questionId);
                }
            }
        });

        // 更新全选复选框
        const selectAllCheckbox = document.getElementById('selectAll') || document.getElementById('headerCheckbox');
        if (selectAllCheckbox) {
            const currentPageIds = this.questions.map(q => q.id);
            const selectedInCurrentPage = currentPageIds.filter(id => this.selectedQuestions.has(id));

            if (selectedInCurrentPage.length === 0) {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = false;
            } else if (selectedInCurrentPage.length === currentPageIds.length) {
                selectAllCheckbox.checked = true;
                selectAllCheckbox.indeterminate = false;
            } else {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = true;
            }
        }

        // 更新卡片选择状态
        const cards = document.querySelectorAll('.question-card');
        cards.forEach((card, index) => {
            const question = this.questions[index];
            if (question && this.selectedQuestions.has(question.id)) {
                card.classList.add('selected');
            } else {
                card.classList.remove('selected');
            }
        });

        // 更新表格行选择状态
        const rows = document.querySelectorAll('#questionsTableBody tr');
        rows.forEach((row, index) => {
            const question = this.questions[index];
            if (question && this.selectedQuestions.has(question.id)) {
                row.classList.add('selected');
            } else {
                row.classList.remove('selected');
            }
        });
    }

    /**
     * 更新批量操作显示
     */
    updateBatchActions() {
        const selectedCount = this.selectedQuestions.size;
        const batchActions = document.querySelector('.batch-actions');

        if (selectedCount > 0) {
            if (!batchActions) {
                // 创建批量操作栏
                const batchHtml = `
                    <div class="batch-actions show">
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="selected-count">已选择 ${selectedCount} 个题目</span>
                            <div class="btn-group">
                                <button class="btn btn-outline-success btn-sm" onclick="batchVerify(true)">
                                    <i class="bi bi-check-all me-1"></i>批量验证
                                </button>
                                <button class="btn btn-outline-warning btn-sm" onclick="batchVerify(false)">
                                    <i class="bi bi-x-circle me-1"></i>取消验证
                                </button>
                                <button class="btn btn-outline-danger btn-sm" onclick="batchDelete()">
                                    <i class="bi bi-trash me-1"></i>批量删除
                                </button>
                                <button class="btn btn-outline-secondary btn-sm" onclick="questionManager.clearSelection()">
                                    <i class="bi bi-x me-1"></i>取消选择
                                </button>
                            </div>
                        </div>
                    </div>
                `;
                const cardBody = document.querySelector('.card-body');
                if (cardBody) {
                    cardBody.insertAdjacentHTML('afterbegin', batchHtml);
                }
            } else {
                batchActions.classList.add('show');
                batchActions.querySelector('.selected-count').textContent = `已选择 ${selectedCount} 个题目`;
            }
        } else {
            if (batchActions) {
                batchActions.classList.remove('show');
            }
        }
    }

    /**
     * 处理题目类型变化
     */
    handleQuestionTypeChange() {
        const questionType = document.getElementById('questionType').value;

        // 隐藏所有选项和答案区域
        document.getElementById('choiceOptions').style.display = 'none';
        document.getElementById('trueFalseOptions').style.display = 'none';
        document.getElementById('singleAnswer').style.display = 'none';
        document.getElementById('multipleAnswer').style.display = 'none';
        document.getElementById('trueFalseAnswer').style.display = 'none';

        // 清空所有输入
        this.clearFormInputs();

        // 根据题目类型显示相应的区域
        switch (questionType) {
            case '单选题':
                document.getElementById('choiceOptions').style.display = 'block';
                document.getElementById('singleAnswer').style.display = 'block';
                this.setRequiredFields(['optionA', 'optionB', 'correctAnswer']);
                break;
            case '多选题':
                document.getElementById('choiceOptions').style.display = 'block';
                document.getElementById('multipleAnswer').style.display = 'block';
                this.setRequiredFields(['optionA', 'optionB']);
                break;
            case '判断题':
                document.getElementById('trueFalseOptions').style.display = 'block';
                document.getElementById('trueFalseAnswer').style.display = 'block';
                this.setRequiredFields([]);
                break;
        }
    }

    /**
     * 清空表单输入
     */
    clearFormInputs() {
        // 清空选项输入
        ['optionA', 'optionB', 'optionC', 'optionD'].forEach(id => {
            const element = document.getElementById(id);
            if (element) element.value = '';
        });

        // 清空答案选择
        const correctAnswerElement = document.getElementById('correctAnswer');
        if (correctAnswerElement) {
            correctAnswerElement.value = '';
        }

        // 清空多选答案
        document.querySelectorAll('input[name="correctAnswers"]').forEach(checkbox => {
            checkbox.checked = false;
        });

        // 清空判断题答案
        document.querySelectorAll('input[name="trueFalseCorrect"]').forEach(radio => {
            radio.checked = false;
        });
    }

    /**
     * 设置必填字段
     */
    setRequiredFields(fields) {
        // 移除所有required属性
        document.querySelectorAll('#questionForm input, #questionForm select').forEach(element => {
            element.removeAttribute('required');
        });

        // 添加必填属性
        ['questionText', 'userImage', ...fields].forEach(fieldId => {
            const element = document.getElementById(fieldId);
            if (element) element.setAttribute('required', 'required');
        });
    }

    /**
     * 重置表单
     */
    resetForm() {
        document.getElementById('questionForm').reset();
        this.isEditing = false;
        this.editingId = null;

        // 重置模态框标题
        document.getElementById('questionModalTitle').innerHTML =
            '<i class="bi bi-plus-circle me-2"></i>新增题目';

        // 隐藏所有选项区域
        this.handleQuestionTypeChange();

        // 生成新的缓存键哈希
        document.getElementById('cacheKeyHash').value = API.generateHash();
    }
}

// 全局实例
let questionManager;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    questionManager = new QuestionManager();
});

// 全局函数
window.refreshData = async function() {
    await questionManager.loadQuestions();
    await questionManager.updateStatistics();
    Utils.showToast('数据已刷新', 'success');
};

window.resetFilters = function() {
    document.getElementById('filterForm').reset();
    questionManager.setDefaultDateRange();
    questionManager.handleFilter();
};

window.toggleView = function(view) {
    questionManager.toggleView(view);
};

window.changePage = function(page) {
    questionManager.changePage(page);
};

window.toggleSelectAll = function() {
    questionManager.toggleSelectAll();
};

window.handleQuestionTypeChange = function() {
    questionManager.handleQuestionTypeChange();
};

/**
 * 显示创建题目模态框
 */
window.showCreateModal = function() {
    questionManager.resetForm();
    const modal = new bootstrap.Modal(document.getElementById('questionModal'));
    modal.show();
};

/**
 * 编辑题目
 */
window.editQuestion = async function(id) {
    try {
        const question = await API.getQuestionById(id);

        // 设置编辑状态
        questionManager.isEditing = true;
        questionManager.editingId = id;

        // 更新模态框标题
        document.getElementById('questionModalTitle').innerHTML =
            '<i class="bi bi-pencil me-2"></i>编辑题目';

        // 填充表单数据
        document.getElementById('questionId').value = id;
        document.getElementById('questionType').value = question.question_type;
        document.getElementById('questionText').value = question.question_text;
        document.getElementById('analysis').value = question.analysis || '';
        document.getElementById('userImage').value = question.user_image || '';
        document.getElementById('imageUrl').value = question.image_url || '';
        document.getElementById('isVerified').value = question.is_verified;
        document.getElementById('cacheKeyHash').value = question.cache_key_hash || '';

        // 触发题目类型变化以显示相应的选项
        questionManager.handleQuestionTypeChange();

        // 填充选项数据
        if (question.options) {
            Object.keys(question.options).forEach(key => {
                const inputId = key === 'Y' ? 'optionY' : key === 'N' ? 'optionN' : `option${key}`;
                const element = document.getElementById(inputId);
                if (element) {
                    element.value = question.options[key];
                }
            });
        }

        // 填充答案数据
        if (question.answer && question.answer.correct) {
            const correct = question.answer.correct;

            if (question.question_type === '单选题') {
                document.getElementById('correctAnswer').value = correct;
            } else if (question.question_type === '多选题') {
                if (Array.isArray(correct)) {
                    correct.forEach(answer => {
                        const checkbox = document.querySelector(`input[name="correctAnswers"][value="${answer}"]`);
                        if (checkbox) checkbox.checked = true;
                    });
                }
            } else if (question.question_type === '判断题') {
                const radio = document.querySelector(`input[name="trueFalseCorrect"][value="${correct}"]`);
                if (radio) radio.checked = true;
            }
        }

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('questionModal'));
        modal.show();

    } catch (error) {
        console.error('获取题目详情失败:', error);
        Utils.showToast('获取题目详情失败: ' + error.message, 'error');
    }
};

/**
 * 保存题目
 */
window.saveQuestion = async function() {
    try {
        const form = document.getElementById('questionForm');
        const formData = new FormData(form);

        // 验证表单
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        // 构建题目数据
        const questionData = {
            question_type: formData.get('questionType'),
            question_text: formData.get('questionText'),
            analysis: formData.get('analysis') || '',
            user_image: formData.get('userImage'),
            image_url: formData.get('imageUrl') || '',
            is_verified: parseInt(formData.get('isVerified')) || 0,
            cache_key_hash: formData.get('cacheKeyHash') || API.generateHash()
        };

        // 根据题目类型设置选项和答案
        const questionType = questionData.question_type;

        if (questionType === '单选题' || questionType === '多选题') {
            // 设置选项
            ['A', 'B', 'C', 'D'].forEach(option => {
                const value = formData.get(`option${option}`);
                if (value && value.trim()) {
                    questionData[`option_${option.toLowerCase()}`] = value.trim();
                }
            });

            // 设置答案
            if (questionType === '单选题') {
                const correctAnswer = formData.get('correctAnswer');
                if (!correctAnswer) {
                    Utils.showToast('请选择正确答案', 'warning');
                    return;
                }
                questionData.answer = { correct: correctAnswer };
            } else {
                const correctAnswers = formData.getAll('correctAnswers');
                if (correctAnswers.length === 0) {
                    Utils.showToast('请至少选择一个正确答案', 'warning');
                    return;
                }
                questionData.answer = { correct: correctAnswers };
            }
        } else if (questionType === '判断题') {
            // 设置判断题选项
            questionData.option_y = '正确';
            questionData.option_n = '错误';

            // 设置答案
            const correctAnswer = formData.get('trueFalseCorrect');
            if (!correctAnswer) {
                Utils.showToast('请选择正确答案', 'warning');
                return;
            }
            questionData.answer = { correct: correctAnswer };
        }

        // 保存题目
        let result;
        if (questionManager.isEditing) {
            result = await API.updateQuestion(questionManager.editingId, questionData);
            Utils.showToast('题目更新成功', 'success');
        } else {
            result = await API.createQuestion(questionData);
            Utils.showToast('题目创建成功', 'success');
        }

        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('questionModal'));
        modal.hide();

        // 刷新数据
        await questionManager.loadQuestions();
        await questionManager.updateStatistics();

    } catch (error) {
        console.error('保存题目失败:', error);
        Utils.showToast('保存失败: ' + error.message, 'error');
    }
};

/**
 * 删除题目
 */
window.deleteQuestion = async function(id) {
    try {
        const confirmed = await Utils.showConfirm(
            '确定要删除这道题目吗？删除后无法恢复！',
            '删除确认'
        );

        if (!confirmed) return;

        await API.deleteQuestion(id);
        Utils.showToast('题目删除成功', 'success');

        // 刷新数据
        await questionManager.loadQuestions();
        await questionManager.updateStatistics();

        // 如果删除的题目在选择列表中，移除它
        questionManager.selectedQuestions.delete(id);
        questionManager.updateBatchActions();

    } catch (error) {
        console.error('删除题目失败:', error);
        Utils.showToast('删除失败: ' + error.message, 'error');
    }
};

/**
 * 切换验证状态
 */
window.toggleVerification = async function(id, isVerified) {
    try {
        const action = isVerified ? '验证' : '取消验证';
        const confirmed = await Utils.showConfirm(
            `确定要${action}这道题目吗？`,
            `${action}确认`
        );

        if (!confirmed) return;

        await API.updateQuestion(id, { is_verified: isVerified });
        Utils.showToast(`${action}成功`, 'success');

        // 刷新数据
        await questionManager.loadQuestions();
        await questionManager.updateStatistics();

    } catch (error) {
        console.error('更新验证状态失败:', error);
        Utils.showToast('操作失败: ' + error.message, 'error');
    }
};

/**
 * 批量验证
 */
window.batchVerify = async function(isVerified) {
    try {
        const selectedIds = Array.from(questionManager.selectedQuestions);
        if (selectedIds.length === 0) {
            Utils.showToast('请先选择要操作的题目', 'warning');
            return;
        }

        const action = isVerified ? '验证' : '取消验证';
        const confirmed = await Utils.showConfirm(
            `确定要${action}选中的 ${selectedIds.length} 道题目吗？`,
            `批量${action}`
        );

        if (!confirmed) return;

        const result = await API.batchUpdateVerificationStatus(selectedIds, isVerified);

        if (result.failed > 0) {
            Utils.showToast(`${action}完成，成功 ${result.successful} 个，失败 ${result.failed} 个`, 'warning');
        } else {
            Utils.showToast(`批量${action}成功，共处理 ${result.successful} 个题目`, 'success');
        }

        // 刷新数据
        await questionManager.loadQuestions();
        await questionManager.updateStatistics();
        questionManager.clearSelection();

    } catch (error) {
        console.error('批量验证失败:', error);
        Utils.showToast('批量操作失败: ' + error.message, 'error');
    }
};

/**
 * 批量删除
 */
window.batchDelete = async function() {
    try {
        const selectedIds = Array.from(questionManager.selectedQuestions);
        if (selectedIds.length === 0) {
            Utils.showToast('请先选择要删除的题目', 'warning');
            return;
        }

        const confirmed = await Utils.showConfirm(
            `确定要删除选中的 ${selectedIds.length} 道题目吗？删除后无法恢复！`,
            '批量删除确认'
        );

        if (!confirmed) return;

        const result = await API.batchDeleteQuestions(selectedIds);

        if (result.failed > 0) {
            Utils.showToast(`删除完成，成功 ${result.successful} 个，失败 ${result.failed} 个`, 'warning');
        } else {
            Utils.showToast(`批量删除成功，共删除 ${result.successful} 个题目`, 'success');
        }

        // 刷新数据
        await questionManager.loadQuestions();
        await questionManager.updateStatistics();
        questionManager.clearSelection();

    } catch (error) {
        console.error('批量删除失败:', error);
        Utils.showToast('批量删除失败: ' + error.message, 'error');
    }
};

/**
 * 显示题目详情
 */
window.showQuestionDetail = async function(id) {
    try {
        const question = await API.getQuestionById(id);
        const modal = new bootstrap.Modal(document.getElementById('questionDetailModal'));

        // 构建详情内容
        const content = `
            <div class="detail-section">
                <h6><i class="bi bi-info-circle me-2"></i>基本信息</h6>
                <div class="detail-item">
                    <div class="detail-label">题目ID:</div>
                    <div class="detail-value">${question.id}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">题目类型:</div>
                    <div class="detail-value">${questionManager.getQuestionTypeDisplay(question.question_type)}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">验证状态:</div>
                    <div class="detail-value">${Utils.getStatusBadge(question.is_verified, 'verification')}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">缓存键哈希:</div>
                    <div class="detail-value">${question.cache_key_hash || '-'}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">创建时间:</div>
                    <div class="detail-value">${Utils.formatDateTime(question.created_at)}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">更新时间:</div>
                    <div class="detail-value">${Utils.formatDateTime(question.updated_at)}</div>
                </div>
            </div>

            <div class="detail-section">
                <h6><i class="bi bi-question-circle me-2"></i>题目内容</h6>
                <div class="detail-item">
                    <div class="detail-label">题目:</div>
                    <div class="detail-value">${question.question_text}</div>
                </div>
                ${question.analysis ? `
                    <div class="detail-item">
                        <div class="detail-label">解析:</div>
                        <div class="detail-value">${question.analysis}</div>
                    </div>
                ` : ''}
            </div>

            ${question.options ? `
                <div class="detail-section">
                    <h6><i class="bi bi-list-ul me-2"></i>选项和答案</h6>
                    <div class="options-display">
                        ${Object.keys(question.options).map(key => {
                            // 获取正确答案的选项键
                            let correctKeys = [];
                            // 尝试多种可能的答案数据位置
                            const answerData = question.answer?.correct || question.answer || question.correct_answer;
                            if (answerData) {
                                if (typeof answerData === 'object' && !Array.isArray(answerData)) {
                                    // 答案是对象格式（如示例中的JSON格式）
                                    correctKeys = Object.keys(answerData);
                                } else if (Array.isArray(answerData)) {
                                    // 答案是数组格式
                                    correctKeys = answerData;
                                } else if (typeof answerData === 'string') {
                                    // 答案是字符串格式
                                    correctKeys = [answerData];
                                }
                            }

                            const isCorrect = correctKeys.includes(key);
                            return `
                                <div class="option-display-item ${isCorrect ? 'correct' : ''}">
                                    <strong>${key}:</strong> ${question.options[key]}
                                    ${isCorrect ? ' <i class="bi bi-check-circle-fill"></i>' : ''}
                                </div>
                            `;
                        }).join('')}
                    </div>
                    <div class="detail-item mt-3">
                        <div class="detail-label">正确答案:</div>
                        <div class="detail-value">
                            <span class="badge bg-success">${questionManager.getAnswerDisplay(question)}</span>
                        </div>
                    </div>
                </div>
            ` : ''}

            ${question.user_image || question.image_url ? `
                <div class="detail-section">
                    <h6><i class="bi bi-image me-2"></i>相关图片</h6>
                    <div class="row g-3">
                        ${question.user_image ? `
                            <div class="col-md-6">
                                <div class="text-center">
                                    <p class="mb-2"><strong>用户图片:</strong></p>
                                    <img src="${question.user_image}" class="img-fluid rounded"
                                         style="max-height: 200px; cursor: pointer;"
                                         onclick="showImageModal('${question.user_image}')" alt="用户图片">
                                </div>
                            </div>
                        ` : ''}
                        ${question.image_url ? `
                            <div class="col-md-6">
                                <div class="text-center">
                                    <p class="mb-2"><strong>管理员图片:</strong></p>
                                    <img src="${question.image_url}" class="img-fluid rounded"
                                         style="max-height: 200px; cursor: pointer;"
                                         onclick="showImageModal('${question.image_url}')" alt="管理员图片">
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            ` : ''}
        `;

        document.getElementById('questionDetailContent').innerHTML = content;
        modal.show();

    } catch (error) {
        console.error('获取题目详情失败:', error);
        Utils.showToast('获取详情失败: ' + error.message, 'error');
    }
};

/**
 * 显示图片模态框
 */
window.showImageModal = function(imageUrl) {
    // 创建图片模态框
    const modalId = 'image-modal-' + Utils.generateId();
    const modalHtml = `
        <div class="modal fade" id="${modalId}" tabindex="-1">
            <div class="modal-dialog modal-lg modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="bi bi-image me-2"></i>图片预览
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body text-center">
                        <img src="${imageUrl}" class="img-fluid rounded" alt="图片预览">
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-outline-primary" onclick="Utils.copyToClipboard('${imageUrl}')">
                            <i class="bi bi-clipboard me-1"></i>复制链接
                        </button>
                        <a href="${imageUrl}" target="_blank" class="btn btn-primary">
                            <i class="bi bi-box-arrow-up-right me-1"></i>新窗口打开
                        </a>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modalElement = document.getElementById(modalId);
    const modal = new bootstrap.Modal(modalElement);

    // 模态框关闭后移除DOM
    modalElement.addEventListener('hidden.bs.modal', () => {
        modalElement.remove();
    });

    modal.show();
};

/**
 * 导出题目数据
 */
window.exportQuestions = async function() {
    try {
        if (questionManager.questions.length === 0) {
            Utils.showToast('没有数据可导出', 'warning');
            return;
        }

        // 准备导出数据
        const exportData = questionManager.questions.map(question => {
            const data = {
                'ID': question.id,
                '题目类型': question.question_type,
                '题目内容': question.question_text,
                '验证状态': question.is_verified == 1 ? '已验证' : '未验证',
                '正确答案': questionManager.getAnswerDisplay(question),
                '题目解析': question.analysis || '',
                '用户图片': question.user_image || '',
                '管理员图片': question.image_url || '',
                '创建时间': Utils.formatDateTime(question.created_at),
                '更新时间': Utils.formatDateTime(question.updated_at)
            };

            // 添加选项
            if (question.options) {
                Object.keys(question.options).forEach(key => {
                    data[`选项${key}`] = question.options[key];
                });
            }

            return data;
        });

        const headers = [
            'ID', '题目类型', '题目内容', '验证状态', '正确答案', '题目解析',
            '选项A', '选项B', '选项C', '选项D', '选项Y', '选项N',
            '用户图片', '管理员图片', '创建时间', '更新时间'
        ];

        const filename = `题库数据_${Utils.formatDateTime(new Date(), 'date')}.csv`;
        Utils.exportToCSV(exportData, filename, headers);

    } catch (error) {
        console.error('导出失败:', error);
        Utils.showToast('导出失败: ' + error.message, 'error');
    }
};
