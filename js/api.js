/**
 * API调用封装
 * 基于快速接入文档和完整接入文档的API规范
 */

class APIClient {
    constructor() {
        this.baseURL = 'http://localhost:8080/api/v1';
        this.defaultHeaders = {
            'Content-Type': 'application/json'
        };
    }

    /**
     * 通用API调用方法
     * @param {string} endpoint - API端点
     * @param {object} options - 请求选项
     * @returns {Promise} API响应
     */
    async request(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        const config = {
            headers: {
                ...this.defaultHeaders,
                ...options.headers
            },
            ...options
        };

        try {
            const response = await fetch(url, config);
            const result = await response.json();

            if (result.code === 200 || result.code === 201) {
                return result.data;
            } else {
                throw new Error(result.message || '请求失败');
            }
        } catch (error) {
            console.error('API调用失败:', error);
            throw error;
        }
    }

    /**
     * 构建查询参数
     * @param {object} params - 参数对象
     * @returns {string} 查询字符串
     */
    buildQueryString(params) {
        const searchParams = new URLSearchParams();
        
        Object.keys(params).forEach(key => {
            const value = params[key];
            if (value !== undefined && value !== null && value !== '') {
                searchParams.append(key, value);
            }
        });
        
        return searchParams.toString();
    }

    // ==================== 请求日志相关API ====================

    /**
     * 分页查询请求日志
     * @param {object} filters - 筛选条件
     * @returns {Promise} 日志列表和分页信息
     */
    async getRequestLogs(filters = {}) {
        const params = {
            page: filters.page || 1,
            page_size: filters.pageSize || 50,
            app_id: filters.appId,
            user_id: filters.userId,
            status: filters.status,
            is_manual_checked: filters.isManualChecked,
            start_date: filters.startDate,
            end_date: filters.endDate,
            sort: filters.sort || 'created_at',
            order: filters.order || 'desc'
        };

        const queryString = this.buildQueryString(params);
        return await this.request(`/request-logs?${queryString}`);
    }

    /**
     * 获取单个请求日志
     * @param {number} id - 日志ID
     * @returns {Promise} 日志详情
     */
    async getRequestLogById(id) {
        return await this.request(`/request-logs/${id}`);
    }

    /**
     * 更新人工确认状态
     * @param {number} id - 日志ID
     * @param {boolean} isChecked - 是否已确认
     * @returns {Promise} 更新结果
     */
    async updateManualCheckStatus(id, isChecked) {
        return await this.request(`/request-logs/${id}/manual-check`, {
            method: 'PUT',
            body: JSON.stringify({
                is_manual_checked: isChecked ? 1 : 0
            })
        });
    }

    /**
     * 获取请求日志统计信息
     * @returns {Promise} 统计数据
     */
    async getRequestLogStatistics() {
        return await this.request('/request-logs/statistics');
    }

    // ==================== 题库管理相关API ====================

    /**
     * 分页查询题库
     * @param {object} filters - 筛选条件
     * @returns {Promise} 题目列表和分页信息
     */
    async getQuestions(filters = {}) {
        const params = {
            page: filters.page || 1,
            page_size: filters.pageSize || 50,
            question_type: filters.questionType,
            is_verified: filters.isVerified,
            keyword: filters.keyword,
            start_date: filters.startDate,
            end_date: filters.endDate,
            sort: filters.sort || 'created_at',
            order: filters.order || 'desc'
        };

        const queryString = this.buildQueryString(params);
        return await this.request(`/questions?${queryString}`);
    }

    /**
     * 获取单个题目
     * @param {number} id - 题目ID
     * @returns {Promise} 题目详情
     */
    async getQuestionById(id) {
        return await this.request(`/questions/${id}`);
    }

    /**
     * 创建题目
     * @param {object} questionData - 题目数据
     * @returns {Promise} 创建结果
     */
    async createQuestion(questionData) {
        return await this.request('/questions', {
            method: 'POST',
            body: JSON.stringify(questionData)
        });
    }

    /**
     * 更新题目
     * @param {number} id - 题目ID
     * @param {object} updateData - 更新数据
     * @returns {Promise} 更新结果
     */
    async updateQuestion(id, updateData) {
        return await this.request(`/questions/${id}`, {
            method: 'PUT',
            body: JSON.stringify(updateData)
        });
    }

    /**
     * 删除题目
     * @param {number} id - 题目ID
     * @returns {Promise} 删除结果
     */
    async deleteQuestion(id) {
        return await this.request(`/questions/${id}`, {
            method: 'DELETE'
        });
    }

    // ==================== 便捷方法 ====================

    /**
     * 创建单选题
     * @param {object} data - 题目数据
     * @returns {Promise} 创建结果
     */
    async createSingleChoiceQuestion(data) {
        const questionData = {
            cache_key_hash: data.cacheKeyHash || this.generateHash(),
            question_type: '单选题',
            question_text: data.questionText,
            option_a: data.optionA,
            option_b: data.optionB,
            option_c: data.optionC,
            option_d: data.optionD,
            answer: { correct: data.correctAnswer },
            analysis: data.analysis,
            user_image: data.userImage,
            image_url: data.imageUrl,
            is_verified: data.isVerified ? 1 : 0
        };

        return await this.createQuestion(questionData);
    }

    /**
     * 创建多选题
     * @param {object} data - 题目数据
     * @returns {Promise} 创建结果
     */
    async createMultipleChoiceQuestion(data) {
        const questionData = {
            cache_key_hash: data.cacheKeyHash || this.generateHash(),
            question_type: '多选题',
            question_text: data.questionText,
            option_a: data.optionA,
            option_b: data.optionB,
            option_c: data.optionC,
            option_d: data.optionD,
            answer: { correct: data.correctAnswers },
            analysis: data.analysis,
            user_image: data.userImage,
            image_url: data.imageUrl,
            is_verified: data.isVerified ? 1 : 0
        };

        return await this.createQuestion(questionData);
    }

    /**
     * 创建判断题
     * @param {object} data - 题目数据
     * @returns {Promise} 创建结果
     */
    async createTrueFalseQuestion(data) {
        const questionData = {
            cache_key_hash: data.cacheKeyHash || this.generateHash(),
            question_type: '判断题',
            question_text: data.questionText,
            option_y: '正确',
            option_n: '错误',
            answer: { correct: data.isTrue ? 'Y' : 'N' },
            analysis: data.analysis,
            user_image: data.userImage,
            image_url: data.imageUrl,
            is_verified: data.isVerified ? 1 : 0
        };

        return await this.createQuestion(questionData);
    }

    /**
     * 生成唯一哈希值
     * @returns {string} 哈希值
     */
    generateHash() {
        return 'hash_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * 批量更新题目验证状态
     * @param {Array} ids - 题目ID数组
     * @param {boolean} isVerified - 验证状态
     * @returns {Promise} 更新结果
     */
    async batchUpdateVerificationStatus(ids, isVerified) {
        const promises = ids.map(id => 
            this.updateQuestion(id, { is_verified: isVerified ? 1 : 0 })
        );
        
        try {
            const results = await Promise.allSettled(promises);
            const successful = results.filter(r => r.status === 'fulfilled').length;
            const failed = results.filter(r => r.status === 'rejected').length;
            
            return {
                successful,
                failed,
                total: ids.length
            };
        } catch (error) {
            throw new Error('批量更新失败: ' + error.message);
        }
    }

    /**
     * 批量删除题目
     * @param {Array} ids - 题目ID数组
     * @returns {Promise} 删除结果
     */
    async batchDeleteQuestions(ids) {
        const promises = ids.map(id => this.deleteQuestion(id));
        
        try {
            const results = await Promise.allSettled(promises);
            const successful = results.filter(r => r.status === 'fulfilled').length;
            const failed = results.filter(r => r.status === 'rejected').length;
            
            return {
                successful,
                failed,
                total: ids.length
            };
        } catch (error) {
            throw new Error('批量删除失败: ' + error.message);
        }
    }
}

// 创建全局API实例
const API = new APIClient();

// 导出API实例（如果在模块环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = API;
}
