/**
 * 请求日志页面逻辑
 */

class RequestLogsManager {
    constructor() {
        this.currentPage = 1;
        this.currentFilters = {};
        this.currentView = 'table'; // 'table' 或 'card'
        this.logs = [];
        this.pagination = {};
        this.stats = {};
        
        this.init();
    }

    /**
     * 初始化页面
     */
    init() {
        this.bindEvents();
        this.loadStatistics();
        this.loadLogs();
        
        // 设置默认日期范围（最近7天）
        this.setDefaultDateRange();
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 筛选表单提交
        document.getElementById('filterForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleFilter();
        });

        // 表单字段变化时的防抖搜索
        const formInputs = document.querySelectorAll('#filterForm input, #filterForm select');
        formInputs.forEach(input => {
            if (input.name === 'startDate' || input.name === 'endDate') {
                input.addEventListener('change', Utils.debounce(() => this.handleFilter(), 500));
            }
        });
    }

    /**
     * 设置默认日期范围
     */
    setDefaultDateRange() {
        const endDate = new Date();
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - 7);

        document.querySelector('input[name="endDate"]').value = Utils.formatDateTime(endDate, 'date');
        document.querySelector('input[name="startDate"]').value = Utils.formatDateTime(startDate, 'date');
    }

    /**
     * 加载统计数据
     */
    async loadStatistics() {
        try {
            this.stats = await API.getRequestLogStatistics();
            this.updateStatistics();
        } catch (error) {
            console.error('加载统计数据失败:', error);
            Utils.showToast('加载统计数据失败: ' + error.message, 'error');
        }
    }

    /**
     * 更新统计显示
     */
    updateStatistics() {
        document.getElementById('totalRequests').textContent = 
            Utils.formatNumber(this.stats.total_requests || 0);
        document.getElementById('successRequests').textContent = 
            Utils.formatNumber(this.stats.success_requests || 0);
        document.getElementById('failedRequests').textContent = 
            Utils.formatNumber(this.stats.failed_requests || 0);
        document.getElementById('pendingCheck').textContent = 
            Utils.formatNumber((this.stats.total_requests || 0) - (this.stats.checked_requests || 0));
    }

    /**
     * 获取筛选条件
     */
    getFilters() {
        const formData = new FormData(document.getElementById('filterForm'));
        const filters = {
            page: this.currentPage,
            pageSize: parseInt(formData.get('pageSize')) || 50
        };

        // 添加非空的筛选条件
        ['status', 'isManualChecked', 'startDate', 'endDate', 'appId', 'userId', 'sort', 'order'].forEach(key => {
            const value = formData.get(key);
            if (value && value.trim() !== '') {
                filters[key] = value.trim();
            }
        });

        return filters;
    }

    /**
     * 处理筛选
     */
    async handleFilter() {
        this.currentPage = 1;
        this.currentFilters = this.getFilters();
        await this.loadLogs();
    }

    /**
     * 加载日志数据
     */
    async loadLogs() {
        this.showLoading(true);
        
        try {
            const result = await API.getRequestLogs(this.currentFilters);
            this.logs = result.data || [];
            this.pagination = result.pagination || {};
            
            this.renderLogs();
            this.renderPagination();
            this.updateRecordInfo();
            
        } catch (error) {
            console.error('加载日志数据失败:', error);
            Utils.showToast('加载数据失败: ' + error.message, 'error');
            this.showEmptyState();
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * 渲染日志列表
     */
    renderLogs() {
        if (this.logs.length === 0) {
            this.showEmptyState();
            return;
        }

        this.hideEmptyState();
        
        if (this.currentView === 'table') {
            this.renderTableView();
        } else {
            this.renderCardView();
        }
    }

    /**
     * 渲染表格视图
     */
    renderTableView() {
        const tbody = document.getElementById('logsTableBody');
        tbody.innerHTML = '';

        this.logs.forEach(log => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <span class="fw-bold text-primary">#${log.id}</span>
                </td>
                <td>
                    <div class="small">
                        <div><strong>应用:</strong> ${log.app_name || log.app_id}</div>
                        <div><strong>用户:</strong> ${log.user_id}</div>
                    </div>
                </td>
                <td>
                    ${Utils.getStatusBadge(log.status, 'request')}
                </td>
                <td>
                    ${Utils.getStatusBadge(log.is_manual_checked, 'manual_check')}
                </td>
                <td>
                    ${log.image_url ?
                        `<img src="${log.image_url}" class="image-thumbnail" onclick="showImageModal('${log.image_url}')" alt="请求图片">` :
                        '<span class="text-muted">无图片</span>'
                    }
                </td>
                <td>
                    <div class="response-payload-preview">
                        ${this.renderResponsePayloadPreview(log)}
                    </div>
                </td>
                <td>
                    <div class="token-info">
                        ${this.renderTokenInfo(log)}
                    </div>
                </td>
                <td>
                    <div class="text-truncate" style="max-width: 200px;" title="${log.error_message || ''}">
                        ${log.error_message || '<span class="text-muted">无错误</span>'}
                    </div>
                </td>
                <td>
                    <div class="small">
                        <div>${Utils.formatDateTime(log.created_at, 'date')}</div>
                        <div class="text-muted">${Utils.formatDateTime(log.created_at, 'time')}</div>
                    </div>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn action-btn btn-view" onclick="showLogDetail(${log.id})" title="查看详情">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button class="btn action-btn ${log.is_manual_checked == 1 ? 'btn-uncheck' : 'btn-check'}" 
                                onclick="toggleManualCheck(${log.id}, ${log.is_manual_checked == 1 ? 0 : 1})"
                                title="${log.is_manual_checked == 1 ? '取消确认' : '标记确认'}">
                            <i class="bi ${log.is_manual_checked == 1 ? 'bi-person-dash' : 'bi-person-check'}"></i>
                        </button>
                    </div>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    /**
     * 渲染卡片视图
     */
    renderCardView() {
        const container = document.getElementById('logsCardContainer');
        container.innerHTML = '';

        this.logs.forEach(log => {
            const card = document.createElement('div');
            card.className = 'col-md-6 col-lg-4';
            card.innerHTML = `
                <div class="log-card">
                    <div class="log-card-header">
                        <div class="log-card-id">#${log.id}</div>
                        <div class="log-card-time">${Utils.formatDateTime(log.created_at)}</div>
                    </div>
                    <div class="log-card-body">
                        <div class="mb-2">
                            <strong>应用:</strong> ${log.app_name || log.app_id} | 
                            <strong>用户:</strong> ${log.user_id}
                        </div>
                        <div class="mb-2">
                            ${Utils.getStatusBadge(log.status, 'request')}
                            ${Utils.getStatusBadge(log.is_manual_checked, 'manual_check')}
                        </div>
                        ${log.image_url ?
                            `<div class="mb-2">
                                <img src="${log.image_url}" class="image-thumbnail" onclick="showImageModal('${log.image_url}')" alt="请求图片">
                            </div>` : ''
                        }
                        ${log.response_payload ?
                            `<div class="mb-2">
                                <div class="small">
                                    <strong>响应数据:</strong>
                                    <div class="response-payload-preview">
                                        ${this.renderResponsePayloadPreview(log)}
                                    </div>
                                </div>
                            </div>` : ''
                        }
                        <div class="token-info">
                            ${this.renderTokenInfo(log)}
                        </div>
                        ${log.error_message ? 
                            `<div class="mt-2">
                                <small class="text-danger">
                                    <strong>错误:</strong> ${Utils.truncateText(log.error_message, 50)}
                                </small>
                            </div>` : ''
                        }
                    </div>
                    <div class="log-card-footer">
                        <button class="btn btn-sm btn-outline-primary" onclick="showLogDetail(${log.id})">
                            <i class="bi bi-eye me-1"></i>查看详情
                        </button>
                        <button class="btn btn-sm ${log.is_manual_checked == 1 ? 'btn-outline-secondary' : 'btn-outline-success'}" 
                                onclick="toggleManualCheck(${log.id}, ${log.is_manual_checked == 1 ? 0 : 1})">
                            <i class="bi ${log.is_manual_checked == 1 ? 'bi-person-dash' : 'bi-person-check'} me-1"></i>
                            ${log.is_manual_checked == 1 ? '取消确认' : '标记确认'}
                        </button>
                    </div>
                </div>
            `;
            container.appendChild(card);
        });
    }

    /**
     * 渲染响应数据预览
     */
    renderResponsePayloadPreview(log) {
        if (!log.response_payload) {
            return '<span class="text-muted">无响应数据</span>';
        }

        try {
            // 如果是对象，提取关键信息进行预览
            if (typeof log.response_payload === 'object') {
                const payload = log.response_payload;
                let preview = '';

                // 显示题目类型（如果存在）
                if (payload.question_type) {
                    preview += `<div class="payload-item"><strong>类型:</strong> ${payload.question_type}</div>`;
                }

                // 显示题目内容（截断显示）
                if (payload.question_text) {
                    preview += `<div class="payload-item"><strong>题目:</strong> ${Utils.truncateText(payload.question_text, 30)}</div>`;
                }

                // 显示答案（如果存在）
                if (payload.answer) {
                    const answer = typeof payload.answer === 'object' ?
                        (Array.isArray(payload.answer.correct) ? payload.answer.correct.join(', ') : payload.answer.correct) :
                        payload.answer;
                    preview += `<div class="payload-item"><strong>答案:</strong> ${answer}</div>`;
                }

                // 如果没有提取到关键信息，显示对象的键数量
                if (!preview) {
                    const keys = Object.keys(payload);
                    preview = `<div class="payload-item text-muted">${keys.length} 个字段</div>`;
                }

                return `
                    <div class="response-payload-content" onclick="showLogDetail(${log.id})" style="cursor: pointer;" title="点击查看完整响应数据">
                        ${preview}
                        <div class="payload-item text-primary small">
                            <i class="bi bi-eye me-1"></i>点击查看详情
                        </div>
                    </div>
                `;
            } else {
                // 如果是字符串，直接截断显示
                return `
                    <div class="response-payload-content" onclick="showLogDetail(${log.id})" style="cursor: pointer;" title="点击查看完整响应数据">
                        <div class="payload-item">${Utils.truncateText(String(log.response_payload), 50)}</div>
                        <div class="payload-item text-primary small">
                            <i class="bi bi-eye me-1"></i>点击查看详情
                        </div>
                    </div>
                `;
            }
        } catch (error) {
            return '<span class="text-danger">数据格式错误</span>';
        }
    }

    /**
     * 渲染Token信息
     */
    renderTokenInfo(log) {
        let html = '';

        if (log.qwen_tokens) {
            html += `
                <div class="token-item">
                    <span class="label">Qwen:</span>
                    <span class="value">${Utils.formatNumber(log.qwen_tokens.total_tokens || 0)}</span>
                </div>
            `;
        }

        if (log.deepseek_tokens) {
            html += `
                <div class="token-item">
                    <span class="label">DeepSeek:</span>
                    <span class="value">${Utils.formatNumber(log.deepseek_tokens.total_tokens || 0)}</span>
                </div>
            `;
        }

        return html || '<span class="text-muted">无Token信息</span>';
    }

    /**
     * 渲染分页
     */
    renderPagination() {
        const pagination = document.getElementById('pagination');
        pagination.innerHTML = '';

        if (!this.pagination.total_pages || this.pagination.total_pages <= 1) {
            return;
        }

        const currentPage = this.pagination.page || 1;
        const totalPages = this.pagination.total_pages;

        // 上一页
        const prevLi = document.createElement('li');
        prevLi.className = `page-item ${!this.pagination.has_prev ? 'disabled' : ''}`;
        prevLi.innerHTML = `
            <a class="page-link" href="#" onclick="changePage(${currentPage - 1})">
                <i class="bi bi-chevron-left"></i>
            </a>
        `;
        pagination.appendChild(prevLi);

        // 页码
        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, currentPage + 2);

        if (startPage > 1) {
            const firstLi = document.createElement('li');
            firstLi.className = 'page-item';
            firstLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(1)">1</a>`;
            pagination.appendChild(firstLi);

            if (startPage > 2) {
                const ellipsisLi = document.createElement('li');
                ellipsisLi.className = 'page-item disabled';
                ellipsisLi.innerHTML = `<span class="page-link">...</span>`;
                pagination.appendChild(ellipsisLi);
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            const li = document.createElement('li');
            li.className = `page-item ${i === currentPage ? 'active' : ''}`;
            li.innerHTML = `<a class="page-link" href="#" onclick="changePage(${i})">${i}</a>`;
            pagination.appendChild(li);
        }

        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                const ellipsisLi = document.createElement('li');
                ellipsisLi.className = 'page-item disabled';
                ellipsisLi.innerHTML = `<span class="page-link">...</span>`;
                pagination.appendChild(ellipsisLi);
            }

            const lastLi = document.createElement('li');
            lastLi.className = 'page-item';
            lastLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${totalPages})">${totalPages}</a>`;
            pagination.appendChild(lastLi);
        }

        // 下一页
        const nextLi = document.createElement('li');
        nextLi.className = `page-item ${!this.pagination.has_next ? 'disabled' : ''}`;
        nextLi.innerHTML = `
            <a class="page-link" href="#" onclick="changePage(${currentPage + 1})">
                <i class="bi bi-chevron-right"></i>
            </a>
        `;
        pagination.appendChild(nextLi);
    }

    /**
     * 更新记录信息
     */
    updateRecordInfo() {
        const info = document.getElementById('recordInfo');
        if (this.pagination.total) {
            info.textContent = `共 ${Utils.formatNumber(this.pagination.total)} 条记录`;
        } else {
            info.textContent = '共 0 条记录';
        }
    }

    /**
     * 显示/隐藏加载状态
     */
    showLoading(show) {
        const loadingState = document.getElementById('loadingState');
        const tableView = document.getElementById('tableView');
        const cardView = document.getElementById('cardView');
        
        if (show) {
            loadingState.style.display = 'block';
            tableView.style.display = 'none';
            cardView.style.display = 'none';
            this.hideEmptyState();
        } else {
            loadingState.style.display = 'none';
            if (this.currentView === 'table') {
                tableView.style.display = 'block';
                cardView.style.display = 'none';
            } else {
                tableView.style.display = 'none';
                cardView.style.display = 'block';
            }
        }
    }

    /**
     * 显示空状态
     */
    showEmptyState() {
        document.getElementById('emptyState').style.display = 'block';
        document.getElementById('tableView').style.display = 'none';
        document.getElementById('cardView').style.display = 'none';
    }

    /**
     * 隐藏空状态
     */
    hideEmptyState() {
        document.getElementById('emptyState').style.display = 'none';
    }

    /**
     * 切换视图
     */
    toggleView(view) {
        this.currentView = view;
        
        // 更新按钮状态
        document.getElementById('tableViewBtn').classList.toggle('active', view === 'table');
        document.getElementById('cardViewBtn').classList.toggle('active', view === 'card');
        
        // 重新渲染
        this.renderLogs();
    }

    /**
     * 切换页码
     */
    async changePage(page) {
        if (page < 1 || page > this.pagination.total_pages) return;
        
        this.currentPage = page;
        this.currentFilters.page = page;
        await this.loadLogs();
    }
}

// 全局实例
let requestLogsManager;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    requestLogsManager = new RequestLogsManager();
});

// 全局函数
window.refreshData = async function() {
    await requestLogsManager.loadStatistics();
    await requestLogsManager.loadLogs();
    Utils.showToast('数据已刷新', 'success');
};

window.resetFilters = function() {
    document.getElementById('filterForm').reset();
    requestLogsManager.setDefaultDateRange();
    requestLogsManager.handleFilter();
};

window.toggleView = function(view) {
    requestLogsManager.toggleView(view);
};

window.changePage = function(page) {
    requestLogsManager.changePage(page);
};

/**
 * 显示日志详情
 */
window.showLogDetail = async function(id) {
    try {
        const log = await API.getRequestLogById(id);
        const modal = new bootstrap.Modal(document.getElementById('logDetailModal'));

        // 构建详情内容
        const content = `
            <div class="detail-section">
                <h6><i class="bi bi-info-circle me-2"></i>基本信息</h6>
                <div class="detail-item">
                    <div class="detail-label">日志ID:</div>
                    <div class="detail-value">${log.id}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">应用ID:</div>
                    <div class="detail-value">${log.app_id}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">应用名称:</div>
                    <div class="detail-value">${log.app_name}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">用户ID:</div>
                    <div class="detail-value">${log.user_id}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">请求状态:</div>
                    <div class="detail-value">${Utils.getStatusBadge(log.status, 'request')}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">确认状态:</div>
                    <div class="detail-value">${Utils.getStatusBadge(log.is_manual_checked, 'manual_check')}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">创建时间:</div>
                    <div class="detail-value">${Utils.formatDateTime(log.created_at)}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">更新时间:</div>
                    <div class="detail-value">${Utils.formatDateTime(log.updated_at)}</div>
                </div>
            </div>

            ${log.image_url ? `
                <div class="detail-section">
                    <h6><i class="bi bi-image me-2"></i>请求图片</h6>
                    <div class="text-center">
                        <img src="${log.image_url}" class="img-fluid rounded" style="max-height: 300px; cursor: pointer;"
                             onclick="showImageModal('${log.image_url}')" alt="请求图片">
                    </div>
                </div>
            ` : ''}

            ${log.response_payload ? `
                <div class="detail-section">
                    <h6><i class="bi bi-code-square me-2"></i>响应数据</h6>
                    <div class="json-block">${JSON.stringify(log.response_payload, null, 2)}</div>
                </div>
            ` : ''}

            ${log.qwen_tokens ? `
                <div class="detail-section">
                    <h6><i class="bi bi-cpu me-2"></i>Qwen Token消耗</h6>
                    <div class="detail-item">
                        <div class="detail-label">输入Token:</div>
                        <div class="detail-value">${Utils.formatNumber(log.qwen_tokens.input_tokens || 0)}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">输出Token:</div>
                        <div class="detail-value">${Utils.formatNumber(log.qwen_tokens.output_tokens || 0)}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">总Token:</div>
                        <div class="detail-value"><strong>${Utils.formatNumber(log.qwen_tokens.total_tokens || 0)}</strong></div>
                    </div>
                </div>
            ` : ''}

            ${log.deepseek_tokens ? `
                <div class="detail-section">
                    <h6><i class="bi bi-cpu-fill me-2"></i>DeepSeek Token消耗</h6>
                    <div class="detail-item">
                        <div class="detail-label">提示Token:</div>
                        <div class="detail-value">${Utils.formatNumber(log.deepseek_tokens.prompt_tokens || 0)}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">完成Token:</div>
                        <div class="detail-value">${Utils.formatNumber(log.deepseek_tokens.completion_tokens || 0)}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">总Token:</div>
                        <div class="detail-value"><strong>${Utils.formatNumber(log.deepseek_tokens.total_tokens || 0)}</strong></div>
                    </div>
                </div>
            ` : ''}

            ${log.error_message ? `
                <div class="detail-section">
                    <h6><i class="bi bi-exclamation-triangle me-2"></i>错误信息</h6>
                    <div class="alert alert-danger">
                        <pre class="mb-0">${log.error_message}</pre>
                    </div>
                </div>
            ` : ''}
        `;

        document.getElementById('logDetailContent').innerHTML = content;
        modal.show();

    } catch (error) {
        console.error('获取日志详情失败:', error);
        Utils.showToast('获取详情失败: ' + error.message, 'error');
    }
};

/**
 * 切换人工确认状态
 */
window.toggleManualCheck = async function(id, isChecked) {
    try {
        const action = isChecked ? '确认' : '取消确认';
        const confirmed = await Utils.showConfirm(
            `确定要${action}这条日志吗？`,
            `${action}确认`
        );

        if (!confirmed) return;

        await API.updateManualCheckStatus(id, isChecked);
        Utils.showToast(`${action}成功`, 'success');

        // 刷新数据
        await requestLogsManager.loadLogs();
        await requestLogsManager.loadStatistics();

    } catch (error) {
        console.error('更新确认状态失败:', error);
        Utils.showToast('操作失败: ' + error.message, 'error');
    }
};

/**
 * 显示图片模态框
 */
window.showImageModal = function(imageUrl) {
    // 创建图片模态框
    const modalId = 'image-modal-' + Utils.generateId();
    const modalHtml = `
        <div class="modal fade" id="${modalId}" tabindex="-1">
            <div class="modal-dialog modal-lg modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="bi bi-image me-2"></i>图片预览
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body text-center">
                        <img src="${imageUrl}" class="img-fluid rounded" alt="图片预览">
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-outline-primary" onclick="Utils.copyToClipboard('${imageUrl}')">
                            <i class="bi bi-clipboard me-1"></i>复制链接
                        </button>
                        <a href="${imageUrl}" target="_blank" class="btn btn-primary">
                            <i class="bi bi-box-arrow-up-right me-1"></i>新窗口打开
                        </a>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modalElement = document.getElementById(modalId);
    const modal = new bootstrap.Modal(modalElement);

    // 模态框关闭后移除DOM
    modalElement.addEventListener('hidden.bs.modal', () => {
        modalElement.remove();
    });

    modal.show();
};

/**
 * 导出日志数据
 */
window.exportLogs = async function() {
    try {
        if (requestLogsManager.logs.length === 0) {
            Utils.showToast('没有数据可导出', 'warning');
            return;
        }

        // 准备导出数据
        const exportData = requestLogsManager.logs.map(log => ({
            'ID': log.id,
            '应用ID': log.app_id,
            '应用名称': log.app_name,
            '用户ID': log.user_id,
            '状态': log.status == 1 ? '成功' : '失败',
            '确认状态': log.is_manual_checked == 1 ? '已确认' : '未确认',
            '图片URL': log.image_url || '',
            'Qwen总Token': log.qwen_tokens?.total_tokens || 0,
            'DeepSeek总Token': log.deepseek_tokens?.total_tokens || 0,
            '错误信息': log.error_message || '',
            '创建时间': Utils.formatDateTime(log.created_at),
            '更新时间': Utils.formatDateTime(log.updated_at)
        }));

        const headers = [
            'ID', '应用ID', '应用名称', '用户ID', '状态', '确认状态',
            '图片URL', 'Qwen总Token', 'DeepSeek总Token', '错误信息',
            '创建时间', '更新时间'
        ];

        const filename = `请求日志_${Utils.formatDateTime(new Date(), 'date')}.csv`;
        Utils.exportToCSV(exportData, filename, headers);

    } catch (error) {
        console.error('导出失败:', error);
        Utils.showToast('导出失败: ' + error.message, 'error');
    }
};
