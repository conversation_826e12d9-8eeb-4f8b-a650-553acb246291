<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>题库管理 - 管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="css/common.css" rel="stylesheet">
    <link href="css/question-manager.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.html">
                <i class="bi bi-gear-fill me-2"></i>管理系统
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">
                            <i class="bi bi-house-fill me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="request-logs.html">
                            <i class="bi bi-journal-text me-1"></i>请求日志
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="question-manager.html">
                            <i class="bi bi-collection me-1"></i>题库管理
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container-fluid py-4">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col">
                <h2 class="fw-bold">
                    <i class="bi bi-collection text-primary me-2"></i>题库管理
                </h2>
                <p class="text-muted mb-0">管理题目的增删改查，支持单选题、多选题、判断题</p>
            </div>
            <div class="col-auto">
                <button class="btn btn-success" onclick="showCreateModal()">
                    <i class="bi bi-plus-circle me-1"></i>新增题目
                </button>
                <button class="btn btn-outline-primary" onclick="refreshData()">
                    <i class="bi bi-arrow-clockwise me-1"></i>刷新数据
                </button>
                <button class="btn btn-outline-info" onclick="exportQuestions()">
                    <i class="bi bi-download me-1"></i>导出数据
                </button>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="row mb-4" id="statsCards">
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-number text-primary" id="totalQuestions">-</div>
                    <div class="stat-label">题目总数</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-number text-success" id="verifiedQuestions">-</div>
                    <div class="stat-label">已验证</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-number text-warning" id="unverifiedQuestions">-</div>
                    <div class="stat-label">未验证</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-number text-info" id="todayQuestions">-</div>
                    <div class="stat-label">今日新增</div>
                </div>
            </div>
        </div>

        <!-- 筛选器 -->
        <div class="filter-section">
            <form id="filterForm">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">搜索关键词</label>
                        <div class="search-box">
                            <i class="bi bi-search search-icon"></i>
                            <input type="text" class="form-control" name="keyword" placeholder="搜索题目内容或解析...">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">题目类型</label>
                        <select class="form-select" name="questionType">
                            <option value="">全部类型</option>
                            <option value="单选题">单选题</option>
                            <option value="多选题">多选题</option>
                            <option value="判断题">判断题</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">验证状态</label>
                        <select class="form-select" name="isVerified">
                            <option value="">全部状态</option>
                            <option value="1">已验证</option>
                            <option value="0">未验证</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">开始日期</label>
                        <input type="date" class="form-control" name="startDate">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">结束日期</label>
                        <input type="date" class="form-control" name="endDate">
                    </div>
                    <div class="col-md-1 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="bi bi-search"></i>
                        </button>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-2">
                        <label class="form-label">排序字段</label>
                        <select class="form-select" name="sort">
                            <option value="created_at">创建时间</option>
                            <option value="updated_at">更新时间</option>
                            <option value="question_type">题目类型</option>
                            <option value="is_verified">验证状态</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">排序方向</label>
                        <select class="form-select" name="order">
                            <option value="desc">降序</option>
                            <option value="asc">升序</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">每页显示</label>
                        <select class="form-select" name="pageSize">
                            <option value="20">20条</option>
                            <option value="50" selected>50条</option>
                            <option value="100">100条</option>
                        </select>
                    </div>
                    <div class="col-md-6 d-flex align-items-end">
                        <button type="button" class="btn btn-outline-secondary me-2" onclick="resetFilters()">
                            <i class="bi bi-arrow-counterclockwise me-1"></i>重置
                        </button>
                        <div class="btn-group">
                            <button type="button" class="btn btn-outline-success" onclick="batchVerify(true)">
                                <i class="bi bi-check-all me-1"></i>批量验证
                            </button>
                            <button type="button" class="btn btn-outline-warning" onclick="batchVerify(false)">
                                <i class="bi bi-x-circle me-1"></i>取消验证
                            </button>
                            <button type="button" class="btn btn-outline-danger" onclick="batchDelete()">
                                <i class="bi bi-trash me-1"></i>批量删除
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- 数据表格 -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-table me-2"></i>题目列表
                </h5>
                <div class="d-flex align-items-center">
                    <span class="text-muted me-3" id="recordInfo">共 0 条记录</span>
                    <div class="form-check me-3">
                        <input class="form-check-input" type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                        <label class="form-check-label" for="selectAll">全选</label>
                    </div>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-secondary active" onclick="toggleView('table')" id="tableViewBtn">
                            <i class="bi bi-table"></i>
                        </button>
                        <button class="btn btn-outline-secondary" onclick="toggleView('card')" id="cardViewBtn">
                            <i class="bi bi-grid-3x3-gap"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <!-- 表格视图 -->
                <div id="tableView">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th width="50">
                                        <input type="checkbox" class="form-check-input" id="headerCheckbox" onchange="toggleSelectAll()">
                                    </th>
                                    <th width="80">ID</th>
                                    <th width="100">类型</th>
                                    <th width="350">题目内容</th>
                                    <th width="200">选项</th>
                                    <th width="250">正确答案</th>
                                    <th width="150">题干图片</th>
                                    <th width="150">用户图片</th>
                                    <th width="120">操作</th>
                                </tr>
                            </thead>
                            <tbody id="questionsTableBody">
                                <!-- 数据将通过JavaScript动态加载 -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 卡片视图 -->
                <div id="cardView" style="display: none;">
                    <div class="row g-3 p-3" id="questionsCardContainer">
                        <!-- 卡片将通过JavaScript动态加载 -->
                    </div>
                </div>

                <!-- 空状态 -->
                <div id="emptyState" class="empty-state" style="display: none;">
                    <i class="bi bi-collection"></i>
                    <h5>暂无题目</h5>
                    <p>没有找到符合条件的题目，<a href="#" onclick="showCreateModal()">点击新增题目</a></p>
                </div>

                <!-- 加载状态 -->
                <div id="loadingState" class="text-center py-5" style="display: none;">
                    <div class="loading-spinner me-2"></div>
                    <span>加载中...</span>
                </div>
            </div>
        </div>

        <!-- 分页 -->
        <nav aria-label="分页导航" class="mt-4">
            <ul class="pagination justify-content-center" id="pagination">
                <!-- 分页将通过JavaScript动态生成 -->
            </ul>
        </nav>
    </div>

    <!-- 创建/编辑题目模态框 -->
    <div class="modal fade" id="questionModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="questionModalTitle">
                        <i class="bi bi-plus-circle me-2"></i>新增题目
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="questionForm">
                        <input type="hidden" name="questionId" id="questionId">

                        <!-- 基本信息 -->
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label class="form-label">题目类型 <span class="text-danger">*</span></label>
                                <select class="form-select" name="questionType" id="questionType" required onchange="handleQuestionTypeChange()">
                                    <option value="">请选择题目类型</option>
                                    <option value="单选题">单选题</option>
                                    <option value="多选题">多选题</option>
                                    <option value="判断题">判断题</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">验证状态</label>
                                <select class="form-select" name="isVerified" id="isVerified">
                                    <option value="0">未验证</option>
                                    <option value="1">已验证</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">缓存键哈希</label>
                                <input type="text" class="form-control" name="cacheKeyHash" id="cacheKeyHash" placeholder="自动生成">
                            </div>
                        </div>

                        <!-- 题目内容 -->
                        <div class="mb-3">
                            <label class="form-label">题目内容 <span class="text-danger">*</span></label>
                            <textarea class="form-control" name="questionText" id="questionText" rows="3" required placeholder="请输入题目内容..."></textarea>
                        </div>

                        <!-- 选项区域 -->
                        <div id="optionsSection">
                            <!-- 单选题/多选题选项 -->
                            <div id="choiceOptions" style="display: none;">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">选项A <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" name="optionA" id="optionA" placeholder="选项A内容">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">选项B <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" name="optionB" id="optionB" placeholder="选项B内容">
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">选项C</label>
                                        <input type="text" class="form-control" name="optionC" id="optionC" placeholder="选项C内容（可选）">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">选项D</label>
                                        <input type="text" class="form-control" name="optionD" id="optionD" placeholder="选项D内容（可选）">
                                    </div>
                                </div>
                            </div>

                            <!-- 判断题选项 -->
                            <div id="trueFalseOptions" style="display: none;">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">正确选项</label>
                                        <input type="text" class="form-control" name="optionY" id="optionY" value="正确" readonly>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">错误选项</label>
                                        <input type="text" class="form-control" name="optionN" id="optionN" value="错误" readonly>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 答案区域 -->
                        <div id="answerSection">
                            <!-- 单选题答案 -->
                            <div id="singleAnswer" style="display: none;">
                                <div class="mb-3">
                                    <label class="form-label">正确答案 <span class="text-danger">*</span></label>
                                    <select class="form-select" name="correctAnswer" id="correctAnswer">
                                        <option value="">请选择正确答案</option>
                                        <option value="A">A</option>
                                        <option value="B">B</option>
                                        <option value="C">C</option>
                                        <option value="D">D</option>
                                    </select>
                                </div>
                            </div>

                            <!-- 多选题答案 -->
                            <div id="multipleAnswer" style="display: none;">
                                <div class="mb-3">
                                    <label class="form-label">正确答案 <span class="text-danger">*</span> <small class="text-muted">（可选择多个）</small></label>
                                    <div class="row">
                                        <div class="col-md-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="correctAnswers" value="A" id="answerA">
                                                <label class="form-check-label" for="answerA">A</label>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="correctAnswers" value="B" id="answerB">
                                                <label class="form-check-label" for="answerB">B</label>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="correctAnswers" value="C" id="answerC">
                                                <label class="form-check-label" for="answerC">C</label>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="correctAnswers" value="D" id="answerD">
                                                <label class="form-check-label" for="answerD">D</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 判断题答案 -->
                            <div id="trueFalseAnswer" style="display: none;">
                                <div class="mb-3">
                                    <label class="form-label">正确答案 <span class="text-danger">*</span></label>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="trueFalseCorrect" value="Y" id="answerTrue">
                                                <label class="form-check-label" for="answerTrue">正确</label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="trueFalseCorrect" value="N" id="answerFalse">
                                                <label class="form-check-label" for="answerFalse">错误</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 解析和图片 -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">题目解析</label>
                                <textarea class="form-control" name="analysis" id="analysis" rows="3" placeholder="请输入题目解析..."></textarea>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">用户图片URL <span class="text-danger">*</span></label>
                                <input type="url" class="form-control" name="userImage" id="userImage" required placeholder="https://example.com/image.jpg">
                                <label class="form-label mt-2">管理员图片URL</label>
                                <input type="url" class="form-control" name="imageUrl" id="imageUrl" placeholder="https://example.com/admin-image.jpg">
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveQuestion()">
                        <i class="bi bi-check-circle me-1"></i>保存
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 题目详情模态框 -->
    <div class="modal fade" id="questionDetailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-info-circle me-2"></i>题目详情
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="questionDetailContent">
                    <!-- 详情内容将通过JavaScript动态加载 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/api.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/question-manager.js"></script>
</body>
</html>
