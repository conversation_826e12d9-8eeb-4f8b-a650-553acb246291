<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>请求日志查询 - 管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="css/common.css" rel="stylesheet">
    <link href="css/request-logs.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.html">
                <i class="bi bi-gear-fill me-2"></i>管理系统
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">
                            <i class="bi bi-house-fill me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="request-logs.html">
                            <i class="bi bi-journal-text me-1"></i>请求日志
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="question-manager.html">
                            <i class="bi bi-collection me-1"></i>题库管理
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container-fluid py-4">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col">
                <h2 class="fw-bold">
                    <i class="bi bi-journal-text text-primary me-2"></i>请求日志查询
                </h2>
                <p class="text-muted mb-0">监控API请求状态，分析系统使用情况</p>
            </div>
            <div class="col-auto">
                <button class="btn btn-outline-primary" onclick="refreshData()">
                    <i class="bi bi-arrow-clockwise me-1"></i>刷新数据
                </button>
                <button class="btn btn-outline-success" onclick="exportLogs()">
                    <i class="bi bi-download me-1"></i>导出数据
                </button>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="row mb-4" id="statsCards">
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-number text-primary" id="totalRequests">-</div>
                    <div class="stat-label">总请求数</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-number text-success" id="successRequests">-</div>
                    <div class="stat-label">成功请求</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-number text-danger" id="failedRequests">-</div>
                    <div class="stat-label">失败请求</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-number text-warning" id="pendingCheck">-</div>
                    <div class="stat-label">待确认</div>
                </div>
            </div>
        </div>

        <!-- 筛选器 -->
        <div class="filter-section">
            <form id="filterForm">
                <div class="row g-3">
                    <div class="col-md-2">
                        <label class="form-label">状态</label>
                        <select class="form-select" name="status">
                            <option value="">全部状态</option>
                            <option value="1">成功</option>
                            <option value="0">失败</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">确认状态</label>
                        <select class="form-select" name="isManualChecked">
                            <option value="">全部</option>
                            <option value="1">已确认</option>
                            <option value="0">未确认</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">开始日期</label>
                        <input type="date" class="form-control" name="startDate">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">结束日期</label>
                        <input type="date" class="form-control" name="endDate">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">应用ID</label>
                        <input type="text" class="form-control" name="appId" placeholder="应用ID">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">用户ID</label>
                        <input type="number" class="form-control" name="userId" placeholder="用户ID">
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-2">
                        <label class="form-label">排序字段</label>
                        <select class="form-select" name="sort">
                            <option value="created_at">创建时间</option>
                            <option value="updated_at">更新时间</option>
                            <option value="status">状态</option>
                            <option value="app_id">应用ID</option>
                            <option value="user_id">用户ID</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">排序方向</label>
                        <select class="form-select" name="order">
                            <option value="desc">降序</option>
                            <option value="asc">升序</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">每页显示</label>
                        <select class="form-select" name="pageSize">
                            <option value="20">20条</option>
                            <option value="50" selected>50条</option>
                            <option value="100">100条</option>
                        </select>
                    </div>
                    <div class="col-md-6 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="bi bi-search me-1"></i>查询
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="resetFilters()">
                            <i class="bi bi-arrow-counterclockwise me-1"></i>重置
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- 数据表格 -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-table me-2"></i>请求日志列表
                </h5>
                <div class="d-flex align-items-center">
                    <span class="text-muted me-3" id="recordInfo">共 0 条记录</span>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-secondary" onclick="toggleView('table')" id="tableViewBtn">
                            <i class="bi bi-table"></i>
                        </button>
                        <button class="btn btn-outline-secondary" onclick="toggleView('card')" id="cardViewBtn">
                            <i class="bi bi-grid-3x3-gap"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <!-- 表格视图 -->
                <div id="tableView">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th width="80">ID</th>
                                    <th width="100">应用信息</th>
                                    <th width="120">状态</th>
                                    <th width="120">确认状态</th>
                                    <th width="200">图片</th>
                                    <th width="200">响应数据</th>
                                    <th width="150">Token消耗</th>
                                    <th width="200">错误信息</th>
                                    <th width="180">创建时间</th>
                                    <th width="120">操作</th>
                                </tr>
                            </thead>
                            <tbody id="logsTableBody">
                                <!-- 数据将通过JavaScript动态加载 -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 卡片视图 -->
                <div id="cardView" style="display: none;">
                    <div class="row g-3 p-3" id="logsCardContainer">
                        <!-- 卡片将通过JavaScript动态加载 -->
                    </div>
                </div>

                <!-- 空状态 -->
                <div id="emptyState" class="empty-state" style="display: none;">
                    <i class="bi bi-inbox"></i>
                    <h5>暂无数据</h5>
                    <p>没有找到符合条件的请求日志</p>
                </div>

                <!-- 加载状态 -->
                <div id="loadingState" class="text-center py-5" style="display: none;">
                    <div class="loading-spinner me-2"></div>
                    <span>加载中...</span>
                </div>
            </div>
        </div>

        <!-- 分页 -->
        <nav aria-label="分页导航" class="mt-4">
            <ul class="pagination justify-content-center" id="pagination">
                <!-- 分页将通过JavaScript动态生成 -->
            </ul>
        </nav>
    </div>

    <!-- 详情模态框 -->
    <div class="modal fade" id="logDetailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-info-circle me-2"></i>请求日志详情
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="logDetailContent">
                    <!-- 详情内容将通过JavaScript动态加载 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/api.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/request-logs.js"></script>
</body>
</html>
