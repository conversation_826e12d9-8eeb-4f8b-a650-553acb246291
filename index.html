<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理系统 - 首页</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="css/common.css" rel="stylesheet">
    <style>
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 80px 0;
            text-align: center;
        }
        .feature-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: none;
            border-radius: 15px;
            overflow: hidden;
        }
        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        .stats-section {
            background-color: #f8f9fa;
            padding: 60px 0;
        }
        .stat-card {
            text-align: center;
            padding: 2rem;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #667eea;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.html">
                <i class="bi bi-gear-fill me-2"></i>管理系统
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="index.html">
                            <i class="bi bi-house-fill me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="request-logs.html">
                            <i class="bi bi-journal-text me-1"></i>请求日志
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="question-manager.html">
                            <i class="bi bi-collection me-1"></i>题库管理
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main>
        <!-- 英雄区域 -->
        <section class="hero-section">
            <div class="container">
                <h1 class="display-4 fw-bold mb-4">欢迎使用管理系统</h1>
                <p class="lead mb-5">高效管理请求日志和题库数据，提升工作效率</p>
                <div class="row justify-content-center">
                    <div class="col-auto">
                        <a href="request-logs.html" class="btn btn-light btn-lg me-3">
                            <i class="bi bi-journal-text me-2"></i>查看请求日志
                        </a>
                        <a href="question-manager.html" class="btn btn-outline-light btn-lg">
                            <i class="bi bi-collection me-2"></i>管理题库
                        </a>
                    </div>
                </div>
            </div>
        </section>

        <!-- 功能特性 -->
        <section class="py-5">
            <div class="container">
                <div class="row text-center mb-5">
                    <div class="col">
                        <h2 class="fw-bold">核心功能</h2>
                        <p class="text-muted">强大的管理功能，满足您的各种需求</p>
                    </div>
                </div>
                <div class="row g-4">
                    <div class="col-md-6">
                        <div class="card feature-card h-100">
                            <div class="card-body text-center p-4">
                                <div class="feature-icon text-primary">
                                    <i class="bi bi-journal-text"></i>
                                </div>
                                <h4 class="card-title">请求日志查询</h4>
                                <p class="card-text text-muted">
                                    实时监控API请求状态，支持多条件筛选和统计分析。
                                    查看请求详情、错误信息和token消耗情况。
                                </p>
                                <ul class="list-unstyled text-start">
                                    <li><i class="bi bi-check-circle-fill text-success me-2"></i>实时统计展示</li>
                                    <li><i class="bi bi-check-circle-fill text-success me-2"></i>多条件筛选</li>
                                    <li><i class="bi bi-check-circle-fill text-success me-2"></i>人工确认管理</li>
                                    <li><i class="bi bi-check-circle-fill text-success me-2"></i>详细日志查看</li>
                                </ul>
                                <a href="request-logs.html" class="btn btn-primary">立即使用</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card feature-card h-100">
                            <div class="card-body text-center p-4">
                                <div class="feature-icon text-success">
                                    <i class="bi bi-collection"></i>
                                </div>
                                <h4 class="card-title">题库管理</h4>
                                <p class="card-text text-muted">
                                    全面的题库管理功能，支持单选题、多选题、判断题的增删改查。
                                    智能验证和图片管理。
                                </p>
                                <ul class="list-unstyled text-start">
                                    <li><i class="bi bi-check-circle-fill text-success me-2"></i>多种题型支持</li>
                                    <li><i class="bi bi-check-circle-fill text-success me-2"></i>智能搜索过滤</li>
                                    <li><i class="bi bi-check-circle-fill text-success me-2"></i>验证状态管理</li>
                                    <li><i class="bi bi-check-circle-fill text-success me-2"></i>图片预览功能</li>
                                </ul>
                                <a href="question-manager.html" class="btn btn-success">立即使用</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 统计信息 -->
        <section class="stats-section">
            <div class="container">
                <div class="row text-center mb-5">
                    <div class="col">
                        <h2 class="fw-bold">系统概览</h2>
                        <p class="text-muted">实时数据统计</p>
                    </div>
                </div>
                <div class="row g-4" id="statsContainer">
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-number" id="totalRequests">-</div>
                            <div class="text-muted">总请求数</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-number text-success" id="successRate">-</div>
                            <div class="text-muted">成功率</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-number text-info" id="totalQuestions">-</div>
                            <div class="text-muted">题库总数</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-number text-warning" id="pendingCheck">-</div>
                            <div class="text-muted">待确认</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 页脚 -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>管理系统</h5>
                    <p class="text-muted">高效的数据管理解决方案</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="text-muted mb-0">© 2025 管理系统. 版本 v1.0</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/api.js"></script>
    <script src="js/utils.js"></script>
    <script>
        // 页面加载完成后获取统计数据
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                // 获取请求日志统计
                const logStats = await API.getRequestLogStatistics();
                document.getElementById('totalRequests').textContent = logStats.total_requests || 0;
                document.getElementById('successRate').textContent = (logStats.success_rate || 0) + '%';
                document.getElementById('pendingCheck').textContent = 
                    (logStats.total_requests - logStats.checked_requests) || 0;

                // 获取题库统计（通过查询第一页获取总数）
                const questionStats = await API.getQuestions({ page: 1, page_size: 1 });
                document.getElementById('totalQuestions').textContent = 
                    questionStats.pagination?.total || 0;
            } catch (error) {
                console.error('获取统计数据失败:', error);
                // 显示默认值
                document.getElementById('totalRequests').textContent = '0';
                document.getElementById('successRate').textContent = '0%';
                document.getElementById('totalQuestions').textContent = '0';
                document.getElementById('pendingCheck').textContent = '0';
            }
        });
    </script>
</body>
</html>
