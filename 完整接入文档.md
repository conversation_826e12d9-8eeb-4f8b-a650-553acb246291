# 前端API快速接入指南

## 📋 概述

本文档提供题库管理与请求日志查询功能的前端API接入指南，包含详细的接口说明、参数描述、响应格式和示例代码。

## 🌐 基础信息

- **API基础URL**: `http://localhost:8080/api/v1`
- **Content-Type**: `application/json`
- **响应格式**: JSON
- **分页默认大小**: 50条/页
- **分页最大大小**: 100条/页

## 📊 通用响应格式

所有API接口都遵循统一的响应格式：

```json
{
  "code": 200,           // 状态码：200=成功，400=参数错误，404=未找到，500=服务器错误
  "message": "操作成功",  // 响应消息
  "data": {}            // 响应数据（具体格式见各接口说明）
}
```

## 📝 请求日志管理API

### 1. 分页查询请求日志

**接口地址**: `GET /api/v1/request-logs`

**功能描述**: 分页查询API请求日志，支持多种条件过滤，用于监控API调用情况和分析系统使用状况。

#### 请求参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| page | int | 否 | 1 | 页码，从1开始 |
| page_size | int | 否 | 50 | 每页大小，最大100 |
| app_id | string | 否 | - | 应用ID过滤（当前默认为"1"） |
| user_id | int | 否 | - | 用户ID过滤（当前默认为1） |
| status | int | 否 | - | 请求状态：0=失败，1=成功 |
| is_manual_checked | int | 否 | - | 人工确认状态：0=未确认，1=已确认 |
| start_date | string | 否 | - | 开始日期，格式：YYYY-MM-DD |
| end_date | string | 否 | - | 结束日期，格式：YYYY-MM-DD |
| sort | string | 否 | created_at | 排序字段：id, created_at, updated_at, status, app_id, user_id |
| order | string | 否 | desc | 排序方向：asc=升序，desc=降序 |

#### 响应数据

```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "data": [
      {
        "id": 1,                                    // 日志ID
        "app_id": "1",                             // 应用ID（后期扩展使用）
        "app_name": "1",                           // 应用名称（后期扩展使用）
        "user_id": 1,                              // 用户ID（后期扩展使用）
        "image_url": "https://example.com/img.jpg", // 请求的图片URL
        "response_payload": {                       // API返回的响应数据
          "question_type": "单选题",
          "question_text": "题目内容"
        },
        "qwen_tokens": {                           // Qwen模型token消耗
          "input_tokens": 1098,
          "output_tokens": 347,
          "total_tokens": 1445
        },
        "deepseek_tokens": {                       // DeepSeek模型token消耗
          "prompt_tokens": 1098,
          "completion_tokens": 347,
          "total_tokens": 1445
        },
        "status": 1,                               // 请求状态：0=失败，1=成功
        "error_message": "",                       // 错误信息（失败时）
        "is_manual_checked": 0,                    // 人工确认：0=未确认，1=已确认
        "created_at": "2025-06-12T09:58:09.982+08:00", // 创建时间
        "updated_at": "2025-06-12T09:58:09.982+08:00"  // 更新时间
      }
    ],
    "pagination": {
      "page": 1,           // 当前页码
      "page_size": 50,     // 每页大小
      "total": 100,        // 总记录数
      "total_pages": 2,    // 总页数
      "has_next": true,    // 是否有下一页
      "has_prev": false    // 是否有上一页
    }
  }
}
```

#### 前端调用示例

```javascript
// 基础查询
async function getRequestLogs(page = 1, pageSize = 50) {
  const response = await fetch(`/api/v1/request-logs?page=${page}&page_size=${pageSize}`);
  const result = await response.json();
  return result;
}

// 条件查询
async function getRequestLogsWithFilter(filters = {}) {
  const params = new URLSearchParams();
  
  // 基础分页参数
  params.append('page', filters.page || 1);
  params.append('page_size', filters.pageSize || 50);
  
  // 过滤条件
  if (filters.status !== undefined) params.append('status', filters.status);
  if (filters.isManualChecked !== undefined) params.append('is_manual_checked', filters.isManualChecked);
  if (filters.startDate) params.append('start_date', filters.startDate);
  if (filters.endDate) params.append('end_date', filters.endDate);
  if (filters.appId) params.append('app_id', filters.appId);
  if (filters.userId) params.append('user_id', filters.userId);
  
  // 排序参数
  if (filters.sort) params.append('sort', filters.sort);
  if (filters.order) params.append('order', filters.order);
  
  const response = await fetch(`/api/v1/request-logs?${params.toString()}`);
  const result = await response.json();
  return result;
}

// 使用示例
const logs = await getRequestLogsWithFilter({
  page: 1,
  pageSize: 20,
  status: 1,              // 只查询成功的请求
  isManualChecked: 0,     // 只查询未人工确认的
  startDate: '2025-06-01',
  endDate: '2025-06-12',
  sort: 'created_at',
  order: 'desc'
});
```

### 2. 获取单个请求日志

**接口地址**: `GET /api/v1/request-logs/{id}`

**功能描述**: 根据ID获取单个请求日志的详细信息。

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 请求日志ID（路径参数） |

#### 响应数据

```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "id": 1,
    "app_id": "1",
    "app_name": "1",
    "user_id": 1,
    "image_url": "https://example.com/img.jpg",
    "response_payload": { /* 响应数据 */ },
    "qwen_tokens": { /* token信息 */ },
    "deepseek_tokens": { /* token信息 */ },
    "status": 1,
    "error_message": "",
    "is_manual_checked": 0,
    "created_at": "2025-06-12T09:58:09.982+08:00",
    "updated_at": "2025-06-12T09:58:09.982+08:00"
  }
}
```

#### 前端调用示例

```javascript
async function getRequestLogById(id) {
  const response = await fetch(`/api/v1/request-logs/${id}`);
  const result = await response.json();
  
  if (result.code === 200) {
    return result.data;
  } else {
    throw new Error(result.message);
  }
}
```

### 3. 更新人工确认状态

**接口地址**: `PUT /api/v1/request-logs/{id}/manual-check`

**功能描述**: 更新请求日志的人工确认状态，用于标记已经人工审核的记录。

#### 请求参数

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 请求日志ID |

**请求体**:
```json
{
  "is_manual_checked": 1  // 0=取消确认，1=确认
}
```

#### 响应数据

```json
{
  "code": 200,
  "message": "更新成功"
}
```

#### 前端调用示例

```javascript
async function updateManualCheckStatus(id, isChecked) {
  const response = await fetch(`/api/v1/request-logs/${id}/manual-check`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      is_manual_checked: isChecked ? 1 : 0
    })
  });
  
  const result = await response.json();
  return result;
}

// 使用示例
await updateManualCheckStatus(1, true);  // 标记为已确认
await updateManualCheckStatus(1, false); // 取消确认
```

### 4. 获取统计信息

**接口地址**: `GET /api/v1/request-logs/statistics`

**功能描述**: 获取请求日志的统计信息，包括总请求数、成功率、人工确认数等。

#### 响应数据

```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "total_requests": 1000,      // 总请求数
    "success_requests": 950,     // 成功请求数
    "failed_requests": 50,       // 失败请求数
    "checked_requests": 800,     // 已人工确认数
    "success_rate": 95.0         // 成功率（百分比）
  }
}
```

#### 前端调用示例

```javascript
async function getRequestLogStatistics() {
  const response = await fetch('/api/v1/request-logs/statistics');
  const result = await response.json();
  return result.data;
}

// 使用示例 - 显示统计卡片
const stats = await getRequestLogStatistics();
console.log(`总请求数: ${stats.total_requests}`);
console.log(`成功率: ${stats.success_rate}%`);
console.log(`待确认: ${stats.total_requests - stats.checked_requests}`);

## 📚 题库管理API

### 1. 分页查询题库

**接口地址**: `GET /api/v1/questions`

**功能描述**: 分页查询题库数据，支持多种条件过滤，用于题库内容的浏览和管理。

#### 请求参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| page | int | 否 | 1 | 页码，从1开始 |
| page_size | int | 否 | 50 | 每页大小，最大100 |
| question_type | string | 否 | - | 题目类型：单选题、多选题、判断题 |
| is_verified | int | 否 | - | 验证状态：0=未验证，1=已验证 |
| keyword | string | 否 | - | 搜索关键词（搜索题目内容和解析） |
| start_date | string | 否 | - | 开始日期，格式：YYYY-MM-DD |
| end_date | string | 否 | - | 结束日期，格式：YYYY-MM-DD |
| sort | string | 否 | created_at | 排序字段：id, created_at, updated_at, question_type |
| order | string | 否 | desc | 排序方向：asc=升序，desc=降序 |

#### 响应数据

```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "data": [
      {
        "question_type": "单选题",                    // 题目类型
        "question_text": "以下哪个选项是正确的？",      // 题目内容
        "options": {                                 // 选项（根据题目类型动态显示）
          "A": "选项A内容",
          "B": "选项B内容",
          "C": "选项C内容",
          "D": "选项D内容"
          // 判断题会显示: "Y": "正确", "N": "错误"
        },
        "answer": {                                  // 答案
          "correct": "A"                            // 单选题答案
          // 多选题: {"correct": ["A", "B"]}
          // 判断题: {"correct": "Y"}
        },
        "analysis": "这道题考查的是...",              // 题目解析
        "user_image": "https://example.com/q1.jpg", // 用户上传的原始图片
        "image_url": "",                            // 管理员添加的图片URL（可为空）
        "is_verified": "1"                          // 验证状态：0=未验证，1=已验证
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 50,
      "total": 100,
      "total_pages": 2,
      "has_next": true,
      "has_prev": false
    }
  }
}
```

#### 前端调用示例

```javascript
// 基础查询
async function getQuestions(page = 1, pageSize = 50) {
  const response = await fetch(`/api/v1/questions?page=${page}&page_size=${pageSize}`);
  const result = await response.json();
  return result;
}

// 条件查询
async function getQuestionsWithFilter(filters = {}) {
  const params = new URLSearchParams();

  // 基础分页参数
  params.append('page', filters.page || 1);
  params.append('page_size', filters.pageSize || 50);

  // 过滤条件
  if (filters.questionType) params.append('question_type', filters.questionType);
  if (filters.isVerified !== undefined) params.append('is_verified', filters.isVerified);
  if (filters.keyword) params.append('keyword', filters.keyword);
  if (filters.startDate) params.append('start_date', filters.startDate);
  if (filters.endDate) params.append('end_date', filters.endDate);

  // 排序参数
  if (filters.sort) params.append('sort', filters.sort);
  if (filters.order) params.append('order', filters.order);

  const response = await fetch(`/api/v1/questions?${params.toString()}`);
  const result = await response.json();
  return result;
}

// 使用示例
const questions = await getQuestionsWithFilter({
  page: 1,
  pageSize: 20,
  questionType: '单选题',
  isVerified: 1,
  keyword: '数学',
  sort: 'created_at',
  order: 'desc'
});
```

### 2. 获取单个题目

**接口地址**: `GET /api/v1/questions/{id}`

**功能描述**: 根据ID获取单个题目的详细信息。

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 题目ID（路径参数） |

#### 响应数据

```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "question_type": "单选题",
    "question_text": "题目内容",
    "options": { /* 选项 */ },
    "answer": { /* 答案 */ },
    "analysis": "解析内容",
    "user_image": "https://example.com/img.jpg",
    "image_url": "",
    "is_verified": "1"
  }
}
```

#### 前端调用示例

```javascript
async function getQuestionById(id) {
  const response = await fetch(`/api/v1/questions/${id}`);
  const result = await response.json();

  if (result.code === 200) {
    return result.data;
  } else {
    throw new Error(result.message);
  }
}

### 3. 创建题目

**接口地址**: `POST /api/v1/questions`

**功能描述**: 创建新的题目，支持单选题、多选题、判断题三种类型。

#### 请求参数

**请求体**:
```json
{
  "cache_key_hash": "unique_hash_123",           // 必填：缓存键哈希（唯一标识）
  "question_type": "单选题",                      // 必填：题目类型（单选题/多选题/判断题）
  "question_text": "以下哪个选项是正确的？",        // 必填：题目内容
  "option_a": "选项A内容",                       // 单选题/多选题：选项A
  "option_b": "选项B内容",                       // 单选题/多选题：选项B
  "option_c": "选项C内容",                       // 单选题/多选题：选项C（可选）
  "option_d": "选项D内容",                       // 单选题/多选题：选项D（可选）
  "option_y": "正确",                           // 判断题：正确选项
  "option_n": "错误",                           // 判断题：错误选项
  "answer": {                                   // 必填：答案
    "correct": "A"                             // 单选题：单个答案
    // 多选题: {"correct": ["A", "B"]}
    // 判断题: {"correct": "Y"}
  },
  "analysis": "这道题考查...",                    // 可选：题目解析
  "user_image": "https://example.com/img.jpg",  // 必填：用户上传的原始图片URL
  "image_url": "https://cdn.example.com/q1.jpg", // 可选：管理员添加的图片URL
  "is_verified": 1                              // 可选：验证状态（0/1），默认0
}
```

#### 字段说明

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| cache_key_hash | string | 是 | 缓存键哈希，用于唯一标识题目 |
| question_type | string | 是 | 题目类型，枚举值：单选题、多选题、判断题 |
| question_text | string | 是 | 题目内容文本 |
| option_a | string | 否 | 选项A（单选题/多选题使用） |
| option_b | string | 否 | 选项B（单选题/多选题使用） |
| option_c | string | 否 | 选项C（单选题/多选题使用） |
| option_d | string | 否 | 选项D（单选题/多选题使用） |
| option_y | string | 否 | 正确选项（判断题使用） |
| option_n | string | 否 | 错误选项（判断题使用） |
| answer | object | 否 | 答案对象，格式根据题目类型而定 |
| analysis | string | 否 | 题目解析说明 |
| user_image | string | 是 | 用户上传的原始图片URL |
| image_url | string | 否 | 管理员添加的图片URL |
| is_verified | int | 否 | 验证状态：0=未验证，1=已验证 |

#### 响应数据

```json
{
  "code": 201,
  "message": "创建成功",
  "data": {
    "question_type": "单选题",
    "question_text": "题目内容",
    "options": { /* 选项 */ },
    "answer": { /* 答案 */ },
    "analysis": "解析内容",
    "user_image": "https://example.com/img.jpg",
    "image_url": "",
    "is_verified": "0"
  }
}
```

#### 前端调用示例

```javascript
// 创建单选题
async function createSingleChoiceQuestion(questionData) {
  const response = await fetch('/api/v1/questions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      cache_key_hash: questionData.cacheKeyHash,
      question_type: '单选题',
      question_text: questionData.questionText,
      option_a: questionData.optionA,
      option_b: questionData.optionB,
      option_c: questionData.optionC,
      option_d: questionData.optionD,
      answer: { correct: questionData.correctAnswer },
      analysis: questionData.analysis,
      user_image: questionData.userImage,
      image_url: questionData.imageUrl,
      is_verified: questionData.isVerified ? 1 : 0
    })
  });

  const result = await response.json();
  return result;
}

// 创建多选题
async function createMultipleChoiceQuestion(questionData) {
  const response = await fetch('/api/v1/questions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      cache_key_hash: questionData.cacheKeyHash,
      question_type: '多选题',
      question_text: questionData.questionText,
      option_a: questionData.optionA,
      option_b: questionData.optionB,
      option_c: questionData.optionC,
      option_d: questionData.optionD,
      answer: { correct: questionData.correctAnswers }, // 数组格式：["A", "B"]
      analysis: questionData.analysis,
      user_image: questionData.userImage,
      is_verified: questionData.isVerified ? 1 : 0
    })
  });

  const result = await response.json();
  return result;
}

// 创建判断题
async function createTrueFalseQuestion(questionData) {
  const response = await fetch('/api/v1/questions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      cache_key_hash: questionData.cacheKeyHash,
      question_type: '判断题',
      question_text: questionData.questionText,
      option_y: '正确',
      option_n: '错误',
      answer: { correct: questionData.isTrue ? 'Y' : 'N' },
      analysis: questionData.analysis,
      user_image: questionData.userImage,
      is_verified: questionData.isVerified ? 1 : 0
    })
  });

  const result = await response.json();
  return result;
}

### 4. 更新题目

**接口地址**: `PUT /api/v1/questions/{id}`

**功能描述**: 更新现有题目信息。**注意：id、qwen_raw、deepseek_raw、qwen_parsed字段不允许修改**。

#### 请求参数

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 题目ID |

**请求体**（所有字段都是可选的，只传需要更新的字段）:
```json
{
  "cache_key_hash": "new_hash_456",              // 可选：更新缓存键哈希
  "question_type": "多选题",                      // 可选：更新题目类型
  "question_text": "更新后的题目内容",             // 可选：更新题目内容
  "option_a": "新的选项A",                       // 可选：更新选项A
  "option_b": "新的选项B",                       // 可选：更新选项B
  "option_c": "新的选项C",                       // 可选：更新选项C
  "option_d": "新的选项D",                       // 可选：更新选项D
  "option_y": "正确",                           // 可选：更新判断题正确选项
  "option_n": "错误",                           // 可选：更新判断题错误选项
  "answer": {                                   // 可选：更新答案
    "correct": ["A", "B"]                      // 根据题目类型调整格式
  },
  "analysis": "更新后的解析",                     // 可选：更新解析
  "user_image": "https://new.example.com/img.jpg", // 可选：更新用户图片
  "image_url": "https://new.cdn.com/q1.jpg",    // 可选：更新管理员图片
  "is_verified": 1                              // 可选：更新验证状态
}
```

#### 字段限制说明

**不允许修改的字段**:
- `id` - 主键ID
- `qwen_raw` - Qwen原始响应数据
- `deepseek_raw` - DeepSeek原始响应数据
- `qwen_parsed` - Qwen解析后的数据

**可以修改的字段**:
- `cache_key_hash` - 缓存键哈希
- `question_type` - 题目类型
- `question_text` - 题目内容
- `option_a/b/c/d` - 选择题选项
- `option_y/n` - 判断题选项
- `answer` - 答案
- `analysis` - 解析
- `user_image` - 用户图片URL
- `image_url` - 管理员图片URL
- `is_verified` - 验证状态

#### 响应数据

```json
{
  "code": 200,
  "message": "更新成功",
  "data": {
    "question_type": "多选题",
    "question_text": "更新后的题目内容",
    "options": { /* 更新后的选项 */ },
    "answer": { /* 更新后的答案 */ },
    "analysis": "更新后的解析",
    "user_image": "https://new.example.com/img.jpg",
    "image_url": "https://new.cdn.com/q1.jpg",
    "is_verified": "1"
  }
}
```

#### 前端调用示例

```javascript
// 更新题目（部分字段）
async function updateQuestion(id, updateData) {
  const response = await fetch(`/api/v1/questions/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(updateData)
  });

  const result = await response.json();
  return result;
}

// 使用示例 - 只更新题目内容和解析
await updateQuestion(1, {
  question_text: "这是更新后的题目内容",
  analysis: "这是更新后的解析"
});

// 使用示例 - 更新验证状态
await updateQuestion(1, {
  is_verified: 1
});

// 使用示例 - 更新选项和答案
await updateQuestion(1, {
  option_a: "新选项A",
  option_b: "新选项B",
  answer: { correct: "B" }
});
```

### 5. 删除题目

**接口地址**: `DELETE /api/v1/questions/{id}`

**功能描述**: 物理删除指定的题目。**注意：这是不可逆操作，删除后数据无法恢复**。

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 题目ID（路径参数） |

#### 响应数据

```json
{
  "code": 200,
  "message": "删除成功"
}
```

#### 前端调用示例

```javascript
async function deleteQuestion(id) {
  // 建议在删除前进行确认
  const confirmed = confirm('确定要删除这道题目吗？删除后无法恢复！');
  if (!confirmed) {
    return { cancelled: true };
  }

  const response = await fetch(`/api/v1/questions/${id}`, {
    method: 'DELETE'
  });

  const result = await response.json();
  return result;
}

// 使用示例
try {
  const result = await deleteQuestion(1);
  if (result.cancelled) {
    console.log('用户取消删除');
  } else if (result.code === 200) {
    console.log('删除成功');
    // 刷新题目列表
    await refreshQuestionList();
  }
} catch (error) {
  console.error('删除失败:', error.message);
}

## 🚨 错误处理

### 常见错误码

| 错误码 | 说明 | 处理建议 |
|--------|------|----------|
| 400 | 参数错误 | 检查请求参数格式和必填字段 |
| 404 | 资源不存在 | 检查ID是否正确，资源是否已被删除 |
| 500 | 服务器内部错误 | 稍后重试，或联系技术支持 |

### 错误响应格式

```json
{
  "code": 400,
  "message": "参数错误",
  "error": "question_type 参数必须为 单选题、多选题 或 判断题"
}
```

### 前端错误处理示例

```javascript
// 通用API调用函数，包含错误处理
async function apiCall(url, options = {}) {
  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    });

    const result = await response.json();

    if (result.code === 200 || result.code === 201) {
      return result;
    } else {
      throw new Error(result.message || '请求失败');
    }
  } catch (error) {
    console.error('API调用失败:', error);
    throw error;
  }
}

// 使用示例
try {
  const result = await apiCall('/api/v1/questions', {
    method: 'POST',
    body: JSON.stringify(questionData)
  });
  console.log('创建成功:', result.data);
} catch (error) {
  alert(`操作失败: ${error.message}`);
}
```

## 💡 最佳实践

### 1. 分页加载

```javascript
// 分页组件示例
class QuestionPagination {
  constructor() {
    this.currentPage = 1;
    this.pageSize = 20;
    this.total = 0;
    this.loading = false;
  }

  async loadPage(page = 1, filters = {}) {
    if (this.loading) return;

    this.loading = true;
    try {
      const result = await getQuestionsWithFilter({
        page,
        pageSize: this.pageSize,
        ...filters
      });

      this.currentPage = result.data.pagination.page;
      this.total = result.data.pagination.total;

      return result.data.data;
    } finally {
      this.loading = false;
    }
  }

  async nextPage(filters = {}) {
    if (this.hasNextPage()) {
      return await this.loadPage(this.currentPage + 1, filters);
    }
  }

  hasNextPage() {
    return this.currentPage * this.pageSize < this.total;
  }
}
```

### 2. 搜索防抖

```javascript
// 搜索防抖示例
class QuestionSearch {
  constructor() {
    this.searchTimeout = null;
    this.searchDelay = 500; // 500ms防抖
  }

  search(keyword, callback) {
    // 清除之前的定时器
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }

    // 设置新的定时器
    this.searchTimeout = setTimeout(async () => {
      try {
        const result = await getQuestionsWithFilter({
          keyword,
          page: 1,
          pageSize: 20
        });
        callback(result.data.data);
      } catch (error) {
        console.error('搜索失败:', error);
      }
    }, this.searchDelay);
  }
}
```

### 3. 数据缓存

```javascript
// 简单的内存缓存
class QuestionCache {
  constructor() {
    this.cache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5分钟过期
  }

  set(key, data) {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  get(key) {
    const cached = this.cache.get(key);
    if (!cached) return null;

    // 检查是否过期
    if (Date.now() - cached.timestamp > this.cacheTimeout) {
      this.cache.delete(key);
      return null;
    }

    return cached.data;
  }

  async getQuestion(id) {
    const cacheKey = `question_${id}`;
    let question = this.get(cacheKey);

    if (!question) {
      question = await getQuestionById(id);
      this.set(cacheKey, question);
    }

    return question;
  }
}
```

## 📱 完整前端示例

### React组件示例

```jsx
import React, { useState, useEffect } from 'react';

// 题库管理组件
function QuestionManager() {
  const [questions, setQuestions] = useState([]);
  const [pagination, setPagination] = useState({});
  const [filters, setFilters] = useState({
    page: 1,
    pageSize: 20,
    questionType: '',
    keyword: ''
  });
  const [loading, setLoading] = useState(false);

  // 加载题目列表
  const loadQuestions = async () => {
    setLoading(true);
    try {
      const result = await getQuestionsWithFilter(filters);
      setQuestions(result.data.data);
      setPagination(result.data.pagination);
    } catch (error) {
      alert(`加载失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // 删除题目
  const handleDelete = async (id) => {
    try {
      await deleteQuestion(id);
      alert('删除成功');
      loadQuestions(); // 重新加载列表
    } catch (error) {
      alert(`删除失败: ${error.message}`);
    }
  };

  // 更新验证状态
  const handleVerify = async (id, isVerified) => {
    try {
      await updateQuestion(id, { is_verified: isVerified ? 1 : 0 });
      alert('更新成功');
      loadQuestions(); // 重新加载列表
    } catch (error) {
      alert(`更新失败: ${error.message}`);
    }
  };

  useEffect(() => {
    loadQuestions();
  }, [filters]);

  return (
    <div className="question-manager">
      {/* 搜索和过滤 */}
      <div className="filters">
        <input
          type="text"
          placeholder="搜索题目..."
          value={filters.keyword}
          onChange={(e) => setFilters({...filters, keyword: e.target.value, page: 1})}
        />
        <select
          value={filters.questionType}
          onChange={(e) => setFilters({...filters, questionType: e.target.value, page: 1})}
        >
          <option value="">全部类型</option>
          <option value="单选题">单选题</option>
          <option value="多选题">多选题</option>
          <option value="判断题">判断题</option>
        </select>
      </div>

      {/* 题目列表 */}
      {loading ? (
        <div>加载中...</div>
      ) : (
        <div className="question-list">
          {questions.map(question => (
            <div key={question.id} className="question-item">
              <h3>{question.question_text}</h3>
              <p>类型: {question.question_type}</p>
              <p>验证状态: {question.is_verified === '1' ? '已验证' : '未验证'}</p>
              <div className="actions">
                <button onClick={() => handleVerify(question.id, question.is_verified === '0')}>
                  {question.is_verified === '1' ? '取消验证' : '标记验证'}
                </button>
                <button onClick={() => handleDelete(question.id)}>删除</button>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* 分页 */}
      <div className="pagination">
        <button
          disabled={!pagination.has_prev}
          onClick={() => setFilters({...filters, page: filters.page - 1})}
        >
          上一页
        </button>
        <span>第 {pagination.page} 页，共 {pagination.total_pages} 页</span>
        <button
          disabled={!pagination.has_next}
          onClick={() => setFilters({...filters, page: filters.page + 1})}
        >
          下一页
        </button>
      </div>
    </div>
  );
}

export default QuestionManager;
```

## 📞 技术支持

如果在接入过程中遇到问题，请检查：

1. **网络连接**: 确保能正常访问API服务器
2. **参数格式**: 检查请求参数是否符合接口要求
3. **权限设置**: 确认是否有相应的操作权限
4. **数据格式**: 确保JSON格式正确，字段类型匹配

**联系方式**: 如需技术支持，请联系开发团队。

---

**文档版本**: v1.0
**更新时间**: 2025-06-12
**适用版本**: API v1.0+
```
```
```
```
