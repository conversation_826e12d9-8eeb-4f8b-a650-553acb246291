# 管理系统

基于S1.md需求文档开发的管理系统，包含请求日志查询和题库管理两个核心功能模块。

## 📋 项目概述

本系统严格按照快速接入文档和完整接入文档的API规范开发，提供了现代化的Web界面来管理API请求日志和题库数据。

### 核心功能

#### 🔍 请求日志查询
- **实时统计展示**: 总请求数、成功率、失败数、待确认数
- **多条件筛选**: 支持按状态、确认状态、日期范围、应用ID、用户ID等筛选
- **详细日志查看**: 查看完整的请求详情、响应数据、Token消耗等
- **人工确认管理**: 支持标记和取消确认状态
- **数据导出**: 支持CSV格式导出
- **双视图模式**: 表格视图和卡片视图切换

#### 📚 题库管理
- **多种题型支持**: 单选题、多选题、判断题
- **完整CRUD操作**: 创建、查看、编辑、删除题目
- **智能搜索过滤**: 支持关键词搜索、题型筛选、验证状态筛选
- **批量操作**: 批量验证、取消验证、删除
- **图片管理**: 支持用户图片和管理员图片预览
- **验证状态管理**: 题目验证状态的管理和统计

## 🚀 快速开始

### 环境要求
- 现代浏览器（Chrome、Firefox、Safari、Edge）
- HTTP服务器（用于本地开发）
- API服务器运行在 `http://localhost:8080`

### 安装和运行

1. **克隆或下载项目文件**
   ```bash
   # 确保所有文件都在同一目录下
   ls -la
   # 应该看到以下文件结构：
   # ├── index.html
   # ├── request-logs.html
   # ├── question-manager.html
   # ├── css/
   # ├── js/
   # └── README.md
   ```

2. **启动HTTP服务器**
   ```bash
   # 使用Python（推荐）
   python3 -m http.server 8000
   
   # 或使用Node.js
   npx http-server -p 8000
   
   # 或使用PHP
   php -S localhost:8000
   ```

3. **访问系统**
   打开浏览器访问：`http://localhost:8000`

### API服务器配置

确保API服务器运行在 `http://localhost:8080/api/v1`，如果API地址不同，请修改 `js/api.js` 文件中的 `baseURL`：

```javascript
// js/api.js 第8行
this.baseURL = 'http://your-api-server:port/api/v1';
```

## 📁 项目结构

```
/
├── index.html              # 主页面
├── request-logs.html       # 请求日志查询页面
├── question-manager.html   # 题库管理页面
├── css/
│   ├── common.css         # 通用样式
│   ├── request-logs.css   # 日志页面样式
│   └── question-manager.css # 题库页面样式
├── js/
│   ├── api.js            # API调用封装
│   ├── utils.js          # 工具函数
│   ├── request-logs.js   # 日志页面逻辑
│   └── question-manager.js # 题库页面逻辑
├── 快速接入文档.md        # API快速参考
├── 完整接入文档.md        # 完整API文档
├── S1.md                 # 需求文档
└── README.md             # 项目说明
```

## 🎯 功能特性

### 请求日志查询页面
- **统计卡片**: 实时显示系统统计数据
- **高级筛选**: 多维度条件筛选
- **分页浏览**: 支持大数据量分页显示
- **详情查看**: 点击查看完整日志详情
- **状态管理**: 一键切换人工确认状态
- **数据导出**: CSV格式导出当前筛选结果

### 题库管理页面
- **题目创建**: 支持三种题型的创建
- **智能表单**: 根据题型动态显示相应字段
- **批量选择**: 支持全选和批量操作
- **实时搜索**: 关键词搜索防抖处理
- **图片预览**: 点击图片可放大查看
- **状态统计**: 实时统计各类题目数量

## 🔧 技术栈

- **前端框架**: 原生JavaScript (ES6+)
- **UI框架**: Bootstrap 5.3.0
- **图标库**: Bootstrap Icons 1.10.0
- **样式**: CSS3 + 自定义样式
- **API通信**: Fetch API
- **模块化**: ES6 Class + 函数式编程

## 📖 API文档

系统严格遵循以下API规范：
- [快速接入文档.md](./快速接入文档.md) - API快速参考
- [完整接入文档.md](./完整接入文档.md) - 详细API文档

### 主要API端点

#### 请求日志相关
- `GET /api/v1/request-logs` - 分页查询日志
- `GET /api/v1/request-logs/{id}` - 获取单个日志
- `PUT /api/v1/request-logs/{id}/manual-check` - 更新确认状态
- `GET /api/v1/request-logs/statistics` - 获取统计信息

#### 题库管理相关
- `GET /api/v1/questions` - 分页查询题目
- `GET /api/v1/questions/{id}` - 获取单个题目
- `POST /api/v1/questions` - 创建题目
- `PUT /api/v1/questions/{id}` - 更新题目
- `DELETE /api/v1/questions/{id}` - 删除题目

## 🎨 界面设计

### 设计理念
- **现代化**: 采用现代扁平化设计风格
- **响应式**: 完美适配桌面端和移动端
- **用户友好**: 直观的操作界面和清晰的信息层次
- **高效**: 快速的数据加载和流畅的交互体验

### 色彩方案
- **主色调**: 渐变蓝紫色 (#667eea → #764ba2)
- **成功色**: 绿色 (#28a745)
- **警告色**: 黄色 (#ffc107)
- **危险色**: 红色 (#dc3545)
- **信息色**: 蓝色 (#17a2b8)

## 🔍 使用说明

### 请求日志查询
1. 访问"请求日志"页面
2. 使用筛选器设置查询条件
3. 查看统计卡片了解整体情况
4. 在表格或卡片视图中浏览日志
5. 点击"查看详情"了解完整信息
6. 使用"确认"按钮管理人工确认状态

### 题库管理
1. 访问"题库管理"页面
2. 点击"新增题目"创建题目
3. 选择题目类型并填写相关信息
4. 使用搜索和筛选功能查找题目
5. 批量选择题目进行批量操作
6. 点击操作按钮进行编辑、验证或删除

## 🚨 注意事项

1. **API服务器**: 确保API服务器正常运行且可访问
2. **浏览器兼容**: 建议使用现代浏览器以获得最佳体验
3. **网络连接**: 需要稳定的网络连接以正常加载外部资源
4. **数据安全**: 删除操作不可逆，请谨慎操作

## 📞 技术支持

如果在使用过程中遇到问题，请检查：
1. API服务器是否正常运行
2. 网络连接是否稳定
3. 浏览器控制台是否有错误信息
4. API地址配置是否正确

## 📄 许可证

本项目基于需求文档开发，版权归相关方所有。

---

**版本**: v1.0  
**更新时间**: 2025-01-12  
**开发者**: Augment Agent
