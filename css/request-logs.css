/* 请求日志页面样式 */

/* 统计卡片动画 */
.stat-card {
    transition: all 0.3s ease;
    cursor: pointer;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    line-height: 1;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.875rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 500;
}

/* 筛选器样式 */
.filter-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.filter-section .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.filter-section .form-control,
.filter-section .form-select {
    border-radius: 8px;
    border: 1px solid #ced4da;
    transition: all 0.3s ease;
}

.filter-section .form-control:focus,
.filter-section .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* 表格样式增强 */
.table {
    font-size: 0.9rem;
}

.table thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.8rem;
    padding: 1rem 0.75rem;
}

.table tbody td {
    padding: 0.75rem;
    vertical-align: middle;
    border-bottom: 1px solid #f1f3f4;
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.05);
    transform: translateX(2px);
}

/* 状态徽章样式 */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.375rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-danger {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status-warning {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.status-info {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* 图片预览样式 */
.image-thumbnail {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid #e9ecef;
}

.image-thumbnail:hover {
    transform: scale(1.1);
    border-color: #667eea;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

/* Token信息样式 */
.token-info {
    font-size: 0.8rem;
    line-height: 1.3;
}

.token-info .token-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.25rem;
}

.token-item .label {
    color: #6c757d;
    font-weight: 500;
}

.token-item .value {
    color: #495057;
    font-weight: 600;
}

/* 操作按钮样式 */
.action-btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
    border-radius: 6px;
    transition: all 0.2s ease;
    border: none;
    margin: 0 0.125rem;
}

.action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.btn-view {
    background-color: #17a2b8;
    color: white;
}

.btn-view:hover {
    background-color: #138496;
    color: white;
}

.btn-check {
    background-color: #28a745;
    color: white;
}

.btn-check:hover {
    background-color: #218838;
    color: white;
}

.btn-uncheck {
    background-color: #6c757d;
    color: white;
}

.btn-uncheck:hover {
    background-color: #5a6268;
    color: white;
}

/* 卡片视图样式 */
.log-card {
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 1.5rem;
    background: white;
    transition: all 0.3s ease;
    height: 100%;
}

.log-card:hover {
    border-color: #667eea;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
    transform: translateY(-2px);
}

.log-card-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid #f1f3f4;
}

.log-card-id {
    font-size: 1.1rem;
    font-weight: 700;
    color: #667eea;
}

.log-card-time {
    font-size: 0.8rem;
    color: #6c757d;
}

.log-card-body {
    margin-bottom: 1rem;
}

.log-card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 0.75rem;
    border-top: 1px solid #f1f3f4;
}

/* 详情模态框样式 */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px 15px 0 0;
    border-bottom: none;
}

.modal-header .btn-close {
    filter: invert(1);
}

.detail-section {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.detail-section h6 {
    color: #667eea;
    font-weight: 600;
    margin-bottom: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.detail-item {
    display: flex;
    margin-bottom: 0.5rem;
}

.detail-label {
    font-weight: 600;
    color: #495057;
    min-width: 120px;
    margin-right: 1rem;
}

.detail-value {
    color: #6c757d;
    flex: 1;
    word-break: break-all;
}

/* JSON代码块样式 */
.json-block {
    background-color: #2d3748;
    color: #e2e8f0;
    padding: 1rem;
    border-radius: 8px;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    line-height: 1.5;
    max-height: 300px;
    overflow-y: auto;
    white-space: pre-wrap;
    word-break: break-all;
}

/* 分页样式增强 */
.pagination {
    margin-bottom: 0;
}

.pagination .page-link {
    border-radius: 8px;
    margin: 0 0.125rem;
    border: 1px solid #dee2e6;
    color: #667eea;
    font-weight: 500;
    padding: 0.5rem 0.75rem;
    transition: all 0.2s ease;
}

.pagination .page-link:hover {
    background-color: #667eea;
    border-color: #667eea;
    color: white;
    transform: translateY(-1px);
}

.pagination .page-item.active .page-link {
    background-color: #667eea;
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.pagination .page-item.disabled .page-link {
    color: #adb5bd;
    background-color: #f8f9fa;
}

/* 视图切换按钮 */
.btn-group .btn {
    border-radius: 6px;
    margin: 0 1px;
}

.btn-group .btn.active {
    background-color: #667eea;
    border-color: #667eea;
    color: white;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .stat-number {
        font-size: 2rem;
    }
    
    .filter-section {
        padding: 1rem;
    }
    
    .filter-section .row > div {
        margin-bottom: 0.75rem;
    }
    
    .table {
        font-size: 0.8rem;
    }
    
    .table thead th,
    .table tbody td {
        padding: 0.5rem 0.25rem;
    }
    
    .action-btn {
        padding: 0.125rem 0.25rem;
        font-size: 0.7rem;
        margin: 0.125rem;
    }
    
    .log-card {
        padding: 1rem;
    }
    
    .image-thumbnail {
        width: 40px;
        height: 40px;
    }
}

@media (max-width: 576px) {
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .stat-card {
        margin-bottom: 1rem;
    }
    
    .filter-section .col-md-6 {
        text-align: center;
        margin-top: 1rem;
    }
    
    .table-responsive {
        font-size: 0.75rem;
    }
    
    .modal-dialog {
        margin: 0.5rem;
    }
    
    .detail-item {
        flex-direction: column;
    }
    
    .detail-label {
        min-width: auto;
        margin-right: 0;
        margin-bottom: 0.25rem;
    }
}

/* 打印样式 */
@media print {
    .filter-section,
    .pagination,
    .action-btn,
    .btn-group {
        display: none !important;
    }
    
    .table {
        font-size: 0.8rem;
    }
    
    .stat-card {
        break-inside: avoid;
    }
}
