/* 题库管理页面样式 */

/* 题目类型图标样式 */
.question-type-icon {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.question-type-single {
    background-color: #e3f2fd;
    color: #1976d2;
    border: 1px solid #bbdefb;
}

.question-type-multiple {
    background-color: #e8f5e8;
    color: #388e3c;
    border: 1px solid #c8e6c9;
}

.question-type-truefalse {
    background-color: #fff3e0;
    color: #f57c00;
    border: 1px solid #ffcc02;
}

/* 题目卡片样式 */
.question-card {
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 1.5rem;
    background: white;
    transition: all 0.3s ease;
    height: 100%;
    position: relative;
}

.question-card:hover {
    border-color: #667eea;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
    transform: translateY(-2px);
}

.question-card.selected {
    border-color: #667eea;
    background-color: rgba(102, 126, 234, 0.05);
}

.question-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid #f1f3f4;
}

.question-card-id {
    font-size: 0.9rem;
    font-weight: 700;
    color: #667eea;
}

.question-card-type {
    font-size: 0.8rem;
}

.question-card-body {
    margin-bottom: 1rem;
}

.question-text {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.75rem;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.question-options {
    font-size: 0.85rem;
    color: #6c757d;
    margin-bottom: 0.75rem;
}

.question-options .option-item {
    margin-bottom: 0.25rem;
    padding: 0.25rem 0.5rem;
    background-color: #f8f9fa;
    border-radius: 6px;
    border-left: 3px solid #dee2e6;
}

.question-options .option-item.correct {
    background-color: #d4edda;
    border-left-color: #28a745;
    color: #155724;
    font-weight: 600;
}

.question-card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 0.75rem;
    border-top: 1px solid #f1f3f4;
}

.question-card-actions {
    display: flex;
    gap: 0.25rem;
}

.question-card-meta {
    font-size: 0.75rem;
    color: #6c757d;
}

/* 选择框样式 */
.question-checkbox {
    position: absolute;
    top: 1rem;
    left: 1rem;
    z-index: 10;
}

.question-checkbox input[type="checkbox"] {
    transform: scale(1.2);
}

/* 图片预览样式 */
.image-preview-container {
    position: relative;
    display: inline-block;
}

.image-preview {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid #e9ecef;
}

.image-preview:hover {
    transform: scale(1.05);
    border-color: #667eea;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.7);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 8px;
    font-size: 0.8rem;
}

.image-preview-container:hover .image-overlay {
    opacity: 1;
}

/* 表格行选择样式 */
.table tbody tr.selected {
    background-color: rgba(102, 126, 234, 0.1);
}

.table tbody tr.selected:hover {
    background-color: rgba(102, 126, 234, 0.15);
}

/* 批量操作按钮样式 */
.batch-actions {
    display: none;
    padding: 1rem;
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    border-radius: 8px 8px 0 0;
}

.batch-actions.show {
    display: block;
}

.batch-actions .selected-count {
    font-weight: 600;
    color: #667eea;
}

/* 模态框样式增强 */
.modal-xl {
    max-width: 1200px;
}

.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px 15px 0 0;
    border-bottom: none;
}

.modal-header .btn-close {
    filter: invert(1);
}

/* 表单样式增强 */
.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.form-control:focus,
.form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-check-input:checked {
    background-color: #667eea;
    border-color: #667eea;
}

.form-check-input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* 选项区域样式 */
#optionsSection {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    border-left: 4px solid #667eea;
}

#answerSection {
    background-color: #fff3cd;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    border-left: 4px solid #ffc107;
}

/* 答案选择样式 */
.form-check {
    padding: 0.5rem;
    border-radius: 6px;
    transition: background-color 0.2s ease;
}

.form-check:hover {
    background-color: rgba(102, 126, 234, 0.05);
}

.form-check-input:checked + .form-check-label {
    color: #667eea;
    font-weight: 600;
}

/* 详情模态框样式 */
.detail-section {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.detail-section h6 {
    color: #667eea;
    font-weight: 600;
    margin-bottom: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.detail-item {
    display: flex;
    margin-bottom: 0.5rem;
}

.detail-label {
    font-weight: 600;
    color: #495057;
    min-width: 120px;
    margin-right: 1rem;
}

.detail-value {
    color: #6c757d;
    flex: 1;
    word-break: break-word;
}

/* 选项展示样式 */
.options-display {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.option-display-item {
    padding: 0.5rem;
    background-color: #f8f9fa;
    border-radius: 6px;
    border-left: 3px solid #dee2e6;
    font-size: 0.9rem;
}

.option-display-item.correct {
    background-color: #d4edda;
    border-left-color: #28a745;
    color: #155724;
    font-weight: 600;
}

/* 搜索框增强 */
.search-box {
    position: relative;
}

.search-box .form-control {
    padding-left: 2.5rem;
}

.search-box .search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    z-index: 5;
}

/* 统计卡片动画 */
.stat-card {
    cursor: pointer;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* 操作按钮样式 */
.action-btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
    border-radius: 6px;
    transition: all 0.2s ease;
    border: none;
    margin: 0 0.125rem;
}

.action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.btn-edit {
    background-color: #17a2b8;
    color: white;
}

.btn-edit:hover {
    background-color: #138496;
    color: white;
}

.btn-verify {
    background-color: #28a745;
    color: white;
}

.btn-verify:hover {
    background-color: #218838;
    color: white;
}

.btn-unverify {
    background-color: #ffc107;
    color: #212529;
}

.btn-unverify:hover {
    background-color: #e0a800;
    color: #212529;
}

.btn-delete {
    background-color: #dc3545;
    color: white;
}

.btn-delete:hover {
    background-color: #c82333;
    color: white;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .question-card {
        padding: 1rem;
    }
    
    .question-card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .question-card-footer {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .question-card-actions {
        width: 100%;
        justify-content: center;
    }
    
    .action-btn {
        padding: 0.125rem 0.25rem;
        font-size: 0.7rem;
        margin: 0.125rem;
    }
    
    .image-preview {
        width: 60px;
        height: 60px;
    }
    
    .modal-xl {
        max-width: 95%;
    }
    
    .options-display {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 576px) {
    .filter-section .row > div {
        margin-bottom: 0.75rem;
    }
    
    .batch-actions {
        padding: 0.75rem;
    }
    
    .question-checkbox {
        top: 0.5rem;
        left: 0.5rem;
    }
    
    .question-text {
        -webkit-line-clamp: 2;
    }
    
    .detail-item {
        flex-direction: column;
    }
    
    .detail-label {
        min-width: auto;
        margin-right: 0;
        margin-bottom: 0.25rem;
    }
}

/* 打印样式 */
@media print {
    .filter-section,
    .pagination,
    .action-btn,
    .btn-group,
    .question-checkbox {
        display: none !important;
    }
    
    .question-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    .table {
        font-size: 0.8rem;
    }
}
